{"ExportRequest": {"ReqUrl": "http://************:10050/", "APIEndPoints": {"ExportUrl": "http://************:8025/api/tally/Export", "DocProcessing": "http://************:8025/IndianInvTally/process_doc", "ClientResp": "http://************:8025/IndianInvTally/client_import_tally_response"}, "DetailedReqFilePath": "Exports\\Requests\\DetailedRequestV1.xml", "AbstractReqFilePath": "Exports\\Requests\\AbstractRequestV1.xml", "FilteredGetItemsReqFilePath": "Exports\\Requests\\FilteredGetItems-V2-Request.xml", "ExportDataDirectoryPath": "Exports\\Response", "VersionInfo": "Resources\\VersionInfo.json", "APILIMITS": {"iPagesAllowedPerDay": 51, "iRequestAllowedPerDay": 101}}}
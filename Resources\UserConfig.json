{"ExportRequest": {"ReqUrl": "http://************:10101/", "DetailedReqFilePath": "Exports\\Requests\\DetailedRequestV1.xml", "AbstractReqFilePath": "Exports\\Requests\\AbstractRequestV1.xml", "RequestDir": "Requests", "FilteredGetItemsReqFilePath": "Exports\\Requests\\FilteredGetItems-V3-Request.xml", "AbhinavDayBookPath": "Exports\\Requests\\DayBookRequestV1.xml", "ExportDataDirectoryPath": "Exports\\Response", "VersionInfo": "Resources\\AccuveloctyConfig.json", "APILIMITS": {"iPagesAllowedPerDay": 51, "iRequestAllowedPerDay": 101}, "bSmartVendorDetectAlgo": false, "iSplitPages": 1}, "BackupConfig": {"DailyBackup": 5, "WeeklyBackup": 14, "MonthlyBackup": 21, "__commet__": "0-1 day: 2, 0-7 days: 3, 0-30 days: 4"}, "CONFIG_XML": "C:\\Users\\<USER>\\Desktop\\TallyPrime 6.0\\Config.XML", "ActivityUI": "V2", "_comment_": {"ActivityUI": "V1 – Executes definitive code that merges Excel reports for each voucher type using locally stored data. Note: This version does not make any server calls. V2 – Displays an actionable UI window that allows the user to import specific XML files. Note: It fetches live data from AccuVelocity servers.", "BackupConfig": "For Example:\\n\\nDaily: Prioritizes the most recent backups from the current date.\\n\\nWeekly: Retains backups from the latest days within each week.\\n\\nMonthly: Keeps a distributed set of backups by selecting the most recent data from each week of the month.", "MergedDocumentMultipleVendorDetectionAlgo": "For multi-vendor detection with split pages, if your documents follow a fixed page structure, use the settings: 'bSmartVendorDetectAlgo': false and 'iSplitPages': '1' or '2' (e.g., split every 2 pages). This improves performance and provides faster results.\\n\\nIf you enable 'bSmartVendorDetectAlgo': true, the system will analyze and detect where each vendor's invoice starts within the merged document, which may take more time due to additional processing."}}
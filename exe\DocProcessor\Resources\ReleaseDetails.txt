**************************************************************************

Version: 1.09 (Current Version)
Release Date: 02/03/2025 
TDL Version: 7.0
TDL Release Date: 17/02/2025

Features:  
- Seperated Request Configuration in Developer Config and User Config.

**************************************************************************

Version: 6.0  
Release Date: 17/02/2025
TDL Version: 7.0
TDL Release Date: 17/02/2025

Features:  
- Add Activity Log Feature

**************************************************************************

Version: 5.1 
Release Date: 04/02/2025  
TDL Version: 3.0
TDL Release Date: 30/01/2025  

Features:  
- Fetching Additional Information, Such as API Endpoints, from Resources

**************************************************************************

Version: 5.0
Release Date: 30/01/2025  
TDL Version: 3.0
TDL Release Date: 30/01/2025  

Features:  
- Added Journal Template
- Automated Backup before import
- Accept Button for processing after file selection
- Help Button for user assistance

**************************************************************************

Version: 4.0 
Release Date: 23/01/2025  
TDL Version: 2.5
TDL Release Date: 23/01/2025  

Features:  
- Pull Base Stock Item DB Update
- Quotation Import XML File & Send Imported Response File To AHM Server

**************************************************************************

Version: 3.0
TDL Version: 2.0
Release Date: 03/01/2025  

Features:  
- View Document Processing Report
- Added the ability to export specified Tally data.

**************************************************************************

Version: 2.0 
TDL Version: 2.0
Release Date: 26/12/2024  

Features:  
- Introduced detailed Tally data export functionality.
- Added abstract Tally data export option. 

**************************************************************************

Version: 1.0  
TDL Version: 1.0
Release Date: 20/12/2024  

Features:  
- Process documents efficiently using the executable.  
- Notifications displayed upon completion of document processing.  
- Supports CLI arguments for streamlined operations.  
- Includes informational flags for About Us and Version Details.  
- Debug mode for advanced troubleshooting.

**************************************************************************
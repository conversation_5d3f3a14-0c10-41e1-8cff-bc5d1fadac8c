"""
TestHansgrohe.py - Hansgrohe vendor test for ParagTraders

This file contains the test execution logic and configuration.
Helper functions are in HelperModule.py.

Directory Structure Created:
TestResults/[Input|Reference]/TestHansgrohe/test_ParagHansgrohe/filename_hash/ -> files + Logs
"""

import os
import sys
import json
from datetime import datetime

# Add the Test directory to the path to import HelperModule
test_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if test_dir not in sys.path:
    sys.path.insert(0, test_dir)
from HelperModule import get_file_path, get_file_hash, execute_accuvelocity, compare_csv_files, compare_xml_files, log_message

# ================================
# GLOBAL CONFIGURATION
# ================================

CUSTOMER_NAME = "ParagTraders"
TEST_FILE_NAME = "TestHansgrohe"
TEST_METHOD = "test_ParagHansgrohe"
RELEARN_MODE = False
INPUT_FILE = r"Test\Customer\ParagTraders\FilesForTesting\Hansgrohe_Invoice_IN2410027104.PDF"
LICENSE_FILE = r"Test\Customer\ParagTraders\Licenses\Developer_4_license_DevMode_True.lic"
VOUCHER_TYPE = "purchase-with-inventory"
XML_IGNORE_TAGS = ["VOUCHERNUMBER", "UDF:_UDF_805347380.LIST", "LASTVCHID"]
CSV_IGNORE_COLUMNS = ["Received Date"]
FILE_EXTENSIONS_TO_COMPARE = [".csv", ".xml"]

# ================================
# END OF GLOBAL CONFIGURATION
# ================================


def test_ParagHansgrohe():
    """Main test function for Hansgrohe vendor"""
    print("🚀 Starting Hansgrohe Test for ParagTraders")
    print(f"📁 Test: {TEST_FILE_NAME}.{TEST_METHOD}")
    print(f"🔄 Learning mode: {RELEARN_MODE}")
    print("=" * 50)

    # Setup base directory as current working directory
    base_dir = os.getcwd()

    # Explicitly create Input and Reference directories
    base_test_dir = os.path.join(base_dir, "Test", "Customer", "ParagTraders", "TestResults")
    input_dir_base = os.path.join(base_test_dir, "Input")
    reference_dir_base = os.path.join(base_test_dir, "Reference")
    os.makedirs(input_dir_base, exist_ok=True)
    os.makedirs(reference_dir_base, exist_ok=True)
    log_message("info", f"Created Input directory: {input_dir_base}")
    log_message("info", f"Created Reference directory: {reference_dir_base}")

    # Resolve input and license file paths
    input_file_path = get_file_path(base_dir, INPUT_FILE)
    file_hash = get_file_hash(input_file_path)
    filename = os.path.splitext(os.path.basename(INPUT_FILE))[0]
    unique_id = f"{filename}_{file_hash}"

    # Setup test-specific directory structure
    if RELEARN_MODE:
        test_dir = os.path.join(input_dir_base, TEST_FILE_NAME, TEST_METHOD, unique_id)
        reference_dir = None
    else:
        input_dir = os.path.join(input_dir_base, TEST_FILE_NAME, TEST_METHOD, unique_id)
        reference_dir = os.path.join(reference_dir_base, TEST_FILE_NAME, TEST_METHOD, unique_id)
        if not os.path.exists(input_dir):
            raise ValueError(f"Input directory not found: {input_dir}. Please run in learning mode first.")
        test_dir = reference_dir

    logs_dir = os.path.join(test_dir, "Logs")
    os.makedirs(logs_dir, exist_ok=True)
    log_file = os.path.join(logs_dir, f"test_execution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    log_message("info", f"Starting test: {TEST_FILE_NAME}.{TEST_METHOD}", log_file)
    log_message("info", f"Customer: {CUSTOMER_NAME}", log_file)

    try:
        # Step 1: Execute AccuVelocity
        print("🔄 Step 1: Executing AccuVelocity...")
        success, execution_time = execute_accuvelocity(
            base_dir, INPUT_FILE, LICENSE_FILE, VOUCHER_TYPE, test_dir, logs_dir, log_file
        )

        comparison_results = []

        # Step 2: Compare files if in validation mode
        if not RELEARN_MODE and success:
            print("🔍 Step 2: Comparing generated files...")
            log_message("info", f"Comparing files between {input_dir} and {reference_dir}", log_file)

            # Find all files with specified extensions in input_dir recursively
            input_files = []
            for root, _, files in os.walk(input_dir):
                for file_name in files:
                    if any(file_name.endswith(ext) for ext in FILE_EXTENSIONS_TO_COMPARE):
                        relative_path = os.path.relpath(os.path.join(root, file_name), input_dir)
                        input_files.append(relative_path)

            # Compare files with matching relative paths in reference_dir
            for relative_path in input_files:
                input_file = os.path.join(input_dir, relative_path)
                reference_file = os.path.join(reference_dir, relative_path)

                if os.path.exists(reference_file):
                    if relative_path.endswith('.csv'):
                        result = compare_csv_files(input_file, reference_file, CSV_IGNORE_COLUMNS)
                        result["file_type"] = "CSV Report"
                    elif relative_path.endswith('.xml'):
                        result = compare_xml_files(input_file, reference_file, XML_IGNORE_TAGS)
                        result["file_type"] = "XML Request"
                    else:
                        result = {
                            "file_type": "Unknown",
                            "files_compared": [input_file, reference_file],
                            "identical": False,
                            "error": f"Unsupported file type for comparison: {relative_path}"
                        }
                        log_message("warning", f"Unsupported file type for comparison: {relative_path}", log_file)

                    result["relative_path"] = relative_path
                    comparison_results.append(result)
                    log_message("info", f"Comparison for {relative_path}: {'PASSED' if result['identical'] else 'FAILED'}", log_file)
                else:
                    comparison_results.append({
                        "file_type": "CSV Report" if relative_path.endswith('.csv') else "XML Request" if relative_path.endswith('.xml') else "Unknown",
                        "files_compared": [input_file, reference_file],
                        "relative_path": relative_path,
                        "identical": False,
                        "error": f"Reference file not found: {relative_path}"
                    })
                    log_message("warning", f"Reference file not found: {relative_path}", log_file)

        # Step 3: Generate test report
        print("📝 Step 3: Generating test report...")
        test_info = {
            "success": success,
            "execution_time": execution_time,
            "test_method": TEST_METHOD,
            "customer": CUSTOMER_NAME,
            "relearn_mode": RELEARN_MODE,
            "test_directory": test_dir,
            "logs_directory": logs_dir
        }

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(logs_dir, f"test_report_{timestamp}.json")
        html_report_file = os.path.join(logs_dir, f"test_report_{timestamp}.html")

        report = {
            "test_info": test_info,
            "timestamp": datetime.now().isoformat(),
            "directories": {
                "test_dir": test_dir,
                "reference_dir": reference_dir or "",
                "logs_dir": logs_dir
            },
            "comparison_results": comparison_results,
            "summary": {
                "total_files": len(comparison_results),
                "passed": sum(1 for r in comparison_results if r.get("identical", False)),
                "failed": sum(1 for r in comparison_results if not r.get("identical", True))
            }
        }

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>AccuVelocity Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .passed {{ color: green; }}
        .failed {{ color: red; }}
        .file-comparison {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; }}
        .differences {{ background-color: #fff5f5; padding: 10px; margin: 10px 0; }}
        pre {{ background-color: #f5f5f5; padding: 10px; overflow-x: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>AccuVelocity Test Report</h1>
        <p><strong>Test:</strong> {TEST_METHOD}</p>
        <p><strong>Customer:</strong> {CUSTOMER_NAME}</p>
        <p><strong>Mode:</strong> {'Learning' if RELEARN_MODE else 'Validation'}</p>
        <p><strong>Timestamp:</strong> {report['timestamp']}</p>
        <p><strong>Execution Time:</strong> {execution_time:.2f} seconds</p>
    </div>

    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Files:</strong> {report['summary']['total_files']}</p>
        <p class="passed"><strong>Passed:</strong> {report['summary']['passed']}</p>
        <p class="failed"><strong>Failed:</strong> {report['summary']['failed']}</p>
    </div>

    <div class="details">
        <h2>File Comparison Details</h2>
"""

        for result in comparison_results:
            status = "passed" if result.get("identical", False) else "failed"
            html_content += f"""
        <div class="file-comparison">
            <h3 class="{status}">{'✅' if status == 'passed' else '❌'} {result.get('file_type', 'Unknown')} ({result.get('relative_path', 'Unknown')})</h3>
            <p><strong>Files:</strong> {', '.join(result.get('files_compared', []))}</p>
            <p><strong>Status:</strong> {'Identical' if result.get('identical', False) else 'Different'}</p>
"""
            if result.get('error'):
                html_content += f"<p class='failed'><strong>Error:</strong> {result['error']}</p>"
            if result.get('differences') and len(result['differences']) > 0:
                html_content += f"""
            <div class="differences">
                <h4>Differences:</h4>
                <pre>{''.join(result['differences'][:20])}</pre>
                {f'<p>... and {len(result["differences"]) - 20} more lines</p>' if len(result['differences']) > 20 else ''}
            </div>
"""
            html_content += "</div>"

        html_content += """
    </div>
</body>
</html>
"""

        with open(html_report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        test_info["report_file"] = report_file
        test_info["html_report_file"] = html_report_file
        log_message("info", f"Test report generated: {html_report_file}", log_file)

        # Step 4: Display results
        print("\n📊 Test Results:")
        print(f"  Success: {success}")
        print(f"  Execution time: {execution_time:.2f} seconds")
        print(f"  Test directory: {test_dir}")
        print(f"  Logs directory: {logs_dir}")
        print(f"  Report: {html_report_file}")

        if comparison_results:
            print("\n📈 Comparison Results:")
            for result in comparison_results:
                print(f"  - {result['file_type']} ({result.get('relative_path', 'Unknown')}): {'PASSED' if result.get('identical', False) else 'FAILED'}")
                if result.get('error'):
                    print(f"    Error: {result['error']}")
                elif not result.get('identical', True):
                    print("    Differences:")
                    for line in result['differences'][:5]:
                        print(f"      {line.strip()}")

        if success:
            print("✅ Test completed successfully!")
        else:
            print("❌ Test failed!")

        return test_info

    except Exception as e:
        log_message("error", f"Test error: {str(e)}", log_file)
        print(f"❌ Test failed with error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "execution_time": 0,
            "test_method": TEST_METHOD,
            "customer": CUSTOMER_NAME
        }


if __name__ == "__main__":
    test_ParagHansgrohe()
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <title>AccuVelocity Help Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            box-sizing: border-box;
            text-align: center;
        }

        .sidebar {
            width: 300px;
            background-color: #002147;
            color: white;
            padding: 20px;
            display: flex;
            flex-direction: column;
            /* position: fixed; */
            top: 0;
            bottom: 0; 
            /* position:absolute; */
           
                     
        }

        /* .accuvelocityicon {
            color: white;
        } */

        .sidebar h1 {
            display: flex;
            align-items: center;
            font-size: 24px;
            margin: 0 0 20px;
            margin-right: 10px;
            margin-top: 20px;
            color:#ffffff;      
        }

        /* .sidebar h1 i {
            font-size: 24px;
            margin-right: 10px;
        } */

        .sidebarcontent{
            justify-content: space-between;
        }
        .accuvelocityicon{
            align-items: center;
            justify-content: center;
            font-size: 25px;
        }

        .sidebar .menu-item {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: white;
            padding: 10px 15px;
            /* margin: 10px 10px; */
            border-radius: 5px;
            margin-top: 10px;
            gap: 0px;
            
            padding-top: 10px;
            box-sizing: border-box;
           
              
        }

        .submenu{
            margin-left: 10px;
        }

        

        .sidebar .menu-item i {
            font-size: 20px;
            margin-right: 15px;
            gap:0px;
        }

        .sidebar .menu-item:hover {
            /* background-color: #004080; */
            background-color: #0078d7;
        }

        /* .sidebar .menu-item.active {
            background-color: #0078d7;
        } */

 


        .social-icons {
            margin-top: 450px; 
             justify-content: center;
            align-items: center;  
            margin-left: 20px; 
             position:relative; 
             margin-top: 340px;
            margin-right:100px;
           
        }

        .social-icons h3 {
           
            margin: 20px 0 10px;
            text-align: left;
        }

        .social-icons a {
            text-decoration: none;
            /* display: inline-block; */
            margin-right: 10px;
            color: white;
           
           
        }

        .social-icons a i {
            font-size: 20px;
        }

        
         
         .social-icons a i {
            background: linear-gradient(135deg, #007bff, #00d4ff);
            color: white;
            font-size: 15px;
            padding: 2px 8px;
            border-radius: 100px;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); 
            letter-spacing:1px;
            
             }


             .social-icons a i:hover {
            background: linear-gradient(135deg, #0056b3, #0099cc);
            transform: scale(1.5);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
         }

        

        .main {
            margin-left: 300px;
            flex: 1;
            padding: 20px;
            background-color: #f9f9f9;
            overflow-y: auto;
            /* overflow-x: hidden; */
            height: 100vh;
            box-sizing: border-box;
            margin-left: 340px;
            align-items: center;
            justify-content:center;
            text-align: center;
            position: absolute;
                    
        }

        .main-header { 
            /* font:bold; */
            background: linear-gradient(135deg, #21c6e7, #007bff);
            background-color: #0078d7;
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: -20px -20px 20px -20px;
            margin-bottom: 10px;
            /* position:fixed; */
            width: 160vh;
        }

        .main-header-content{
            margin: 0;
            padding: 0;
            text-align: left;
            line-height: 1px;
            margin-top: 10px;
            
  }
      
        .font-text {
           font-size: 12px;
         }

            .sign-in-button {
            background: linear-gradient(135deg, #007bff, #00d4ff);
            color: white;
            font-size: 18px;
            font-weight: bold;
            padding: 12px 30px;
            margin-right: 20px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            text-transform: uppercase;
            letter-spacing: 1px;
             }

        .sign-in-button:hover {
            background: linear-gradient(135deg, #0056b3, #0099cc);
            transform: scale(1.05);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
         } 


        .section {
            margin-bottom: 20px;
            line-height: 1;
            align-items: center;
            justify-content:center;
            width: 100%;
            margin-top: 120px;

        }

        .section h2 {
            color:  #ffffff;
            text-align: left;
            margin-left: 140px;
            
        }

        .section p {
            line-height: 1.5;
            width:80%;
            margin-left: 140px;
            text-align: left;
            font-family:Arial;
           
        }
        .section h3{
            text-align: left;
            margin-left: 140px;
            padding: 0;
        }

        .section ol {
            text-align: left;
            margin-left: 140px;
            line-height: 23px;

        }

        .section h2{
            /* padding: 10px;
            background-color: #004080;
            box-sizing: border-box;
            width:auto; */
            background-color:  #002147; 
            padding: 20px; 
            margin: 20px auto; 
            width: 80%;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); 

        }

        iframe {
            width: 40%;
            height: 315px;
            border: none;
            margin: 10px 0;
            justify-content: left;
        }

        img {
            width: 100%;
            max-width: 400px;
            height: auto;
            /* margin: 10px 0; */
            margin-left: 140px;
            margin: 0;
            text-align: left;
            justify-content: left;

        }

        a {
            color: #0078d7;
            text-decoration:dotted;
        }

        a:hover {
            text-decoration: underline;
        }

    </style>

</head>

<body>


    <!-- Sidebar -->

 <div class="sidebar">

        <h1 class="accuvelocityicon">
            <a href="https://www.accuvelocity.com">
            <!-- <img src="\\192.168.1.18\developers\DEVELOPER_PUBLIC\interns\mohit\Help Page\Help Page Icons Png\accuvelocityIcon.png"
            style="width: 50px; height:50px; background:transparent";> -->
            AccuVelocity
            </a>
            <!-- AccuVelocity -->

        </h1>
            
        
        <a href="#introduction" class="menu-item active"><i class="fas fa-th-large"></i> Introduction</a>

        <div class="sidebarsunmenu">

             <a href="#process-documents" class="menu-item"  onclick="toggleSubmenu('submenu1')" ><i class="fas fa-file-alt"></i> How to Process Documents</a>

            <div id="submenu1" class="submenu">
              <a href="#process-documents" class="menu-item">General Guide to process Document</a>
              <a href="#purchasewithinv" class="menu-item">Purchase with inventory voucher </a>
              <a href="#selectdelivery" class="menu-item">Sales Delivery Note Voucher </a>
            </div>

        </div>

        
        <a href="#faqs" class="menu-item"><i class="fas fa-question-circle"></i> FAQs</a>
        <a href="#troubleshoot" class="menu-item"><i class="fas fa-tools"></i> Troubleshoot</a>
        <a href="#support" class="menu-item"><i class="fas fa-headset"></i> Support</a>
        <a href="#conclusion" class="menu-item"><i class="fas fa-check-circle"></i> Conclusion</a>

        <div class="social-icons" >
            <h3>Connect with us:</h3>
            <a href="https://www.facebook.com/people/AccuVelocity/61558441675742/" target="_blank"><i class="fab fa-facebook"></i></a>
            <a href="https://www.youtube.com/@AccuVelocity" target="_blank"><i class="fab fa-youtube"></i></a>
            <a href="https://www.instagram.com/accu_velocity/" target="_blank"><i class="fab fa-instagram"></i></a>
            <a href="https://www.linkedin.com/company/accuvelocity?trk=public_post_follow-view-profile" target="_blank"><i class="fab fa-linkedin"></i></a>
        </div>

  
</div>



    <!-- Main Content -->

   


<div class="main">

    <div class="main-header">

        <div class="main-header-content">
         <h2>How can we help?</h2>
         <p class="font-text" >Find guides, solutions, and support to make the most of our services</p>
        </div>  

         <a href="https://www.accuvelocity.com" class="sign-in-button">Sign In</a>

    </div>


       
        <div id="introduction" class="section">

            <h2>1.Introduction</h2>
            <p>Welcome to the AccuVelocity Help Document, a comprehensive guide to mastering the software's functionalities and features.
                 This document offers detailed instructions, tips, and troubleshooting advice to optimize your workflow with AccuVelocity, a state-of-the-art document processing tool. 
                 It includes step-by-step installation instructions, extensive support information, and FAQs to assist with any issues. Additionally, you will find a detailed guide on processing documents effectively, 
                 covering various features and functionalities. This document aims to enhance your experience with AccuVelocity, empowering you to achieve greater efficiency and productivity in your document processing tasks.</p>
            <iframe src="https://www.youtube.com/embed/W0STKYyKa8Y" title="Getting Started Tutorial" allowfullscreen></iframe>
            <p><a href="https://youtu.be/W0STKYyKa8Y" target="_blank">Watch on YouTube</a></p>
          
        </div>


 <div id="process-documents" class="section">

           

     <div id="process-documents" class="section"> 
        <h2>2.How to Process Documents</h2>

             <h3> General Guide to Process Document:</h3>

             <h3>Step-1 AccuVelocity AI:</h3>
             <p>Open Tally and Go To Accuvelocity AI Optioin</p>
             <img src="assets/step1.png" alt="">

             <h3>Step-2 Feature of AccuVelocity AI:</h3>
             <p>Select AccuVelocity Voucher Processing to process your documents
                OR
                Select Quit if you do not want Use the Feature of AccuVelocity AI.
             </p>
             <img src="assets/step2.png" alt="">

             <h3>Step-3 Selecting voucher type:</h3>
             <p>You See three voucher types: <p>
             <p> 
                <ol>
                    <li>Delivery Note</li>
                    <li>Journal Entry</li>
                    <li>Purchase with Inventory</li>
                </ol>
                
            </p>
               
            <p>
                 Please select the voucher type according to the document you want to process.
            </p>
             <img src="assets/step3.png" alt="">

             <h3>Step-4 Selecting path of Documents:</h3>
             <p>Specify Path: Manually enter the path or copy and paste your path.</p>
             <p> If you select "Specify Path" ensure you enter the path without using quotation marks. For example, 
                if your documents are stored in C:\Documents\Tally, enter it exactly as C:\Documents\Tally without any quotes.
                Select from Drive: Alternatively, browse and select the path from your drive.
               
             </p>
             <p>
                <span style="font-weight: 600; font-style: italic ;"> 
                    Note:
                    To provide multiple documents for processing, create a folder and add all the documents to this folder.
                     Then, create a zip archive of this folder and provide the path to this zip file.            
                    This revision clarifies the instructions and corrects grammatical errors.</span>
            </p>
          
             
             <img src="assets/step4.png" alt="">

             <h3>Step-5 Process Documents:</h3>
             <p>Select the "Process Voucher" option to proceed with your documents.</p>
             <img src="assets/step5.png" alt="">

             <h3>Step-6 Document Processing Started:</h3>
             <p>Your document processing has started.</p>
             <img src="assets/step6.png" alt="">

             <h3>Step-7 Completing the Process of Document:</h3>
             <p>You see the details of Your Process Document.</p>
             <img src="assets/step7.png" alt="">

             <h3>Step-8 Home Page of Tally:</h3>
             <p>Go to Home Page of Tally</p>
             <img src="assets/step8.png" alt="">

             <h3>Step-9 Import Vouchers:</h3>
             <p>Depending on the voucher type you selected in <span style="font-weight: 600;"> Step 3</span> follow the corresponding import steps:</p>
             <p>
                   <ol>
                    <li >Import the processed voucher <span style="font-weight: 600;">Delivery Note</span> <a href="#selectdelivery">(link)</a> </li>
                    <li>Import the processed voucher <span style="font-weight: 600;"> Purchase with Inventory</span> <a href="#purchasewithinv">(link)</a></li>
                   </ol>
             
             </p>

     </div>


     <div id="purchasewithinv">

                <h2>Purchase with inventory voucher</h2>

                
                <h3>1.Process Of importing Processed Document:</h3>
                <p>You can start the process in two ways: 
                    You can press the shortcut key TTP
                   OR
                   If you cannot use the shortcut TTP then:  </p>
                   <p> Select Tally Post API</p>       
                
                <img src="assets/pwi_1.png" alt="">
   
                <p>Select API Templates</p>
                <img src="assets/pwi_2.png" alt="">
   
                <p>Select Purchase with Inventory</p>
                <img src="assets/pwi_3.png" alt="">
   
                <h3>2.Importing Documents:</h3>
                <p>You will be redirected to the Home Page of Tally
                   AND 
                   Right side of the interface. You will find the <span style="font-weight: 600;">"Finish Import"</span>  option there. 
                   Select this to complete the import process efficiently.</p>
                <img src="assets/importd_1.png" alt="">
   
                <h3>3.Tally Window:</h3>
                <p>You will see the Tally window command prompt on your screen. 
                   import is successful a message will appear saying "Request was successfully" Press any key to return to the main interface of Tally.</p>
                <img src="assets/tally.png" alt="">
   
                <h3>4.Home Page:</h3>
                <p>You Redirect to Home Page:
                   Go to Day Book  </p>
                <img src="assets/homepage.png" alt="">
   
                <h3>5.Select the Invoice Date:</h3>
                <p>You will see the right side of the page date option. In this, add the date of your Process invoice.</p>
                <img src="assets/invoice.png" alt="">
   
                <h3>6.Processed Invoice Entry Verify:</h3>
                <p>You will see all the entries for this date displayed. Carefully review the entries to ensure all necessary invoices have been processed correctly.</p>
                <img src="assets/invoiceentry.png" alt="">


        </div>

           <div id="selectdelivery">

            <h2>Sales Delivery Note Voucher</h2>

            <p>
                Once you have successfully processed your  document by following the steps mentioned in the 
                <span style="font-weight: 600;"> General Guide to Process Documents</span><a href="#process-documents">(link)</a> 
                and your document type was <span style="font-weight: 600;">Purchase with Inventory</span>  follow the steps below to import your vouchers:
            </p>


            <h3>You Can Start Further Process in Two Ways:</h3>
            <p>You can press the Shortcut Key DSS
               OR
               If you cannot use the shortcut DSS then:  </p>                                                                                                                                        
            <p>Select Display More Reports</p>
            <img src="assets/dnv_1.png" alt="">

            
            <p>Select Statements of Accounts</p>
            <img src="assets/dnv_2.png" alt="">

          
            <p>Select Statistics</p>
            <img src="assets/dnv_3.png" alt="">

            <h3>1.Select AV Purchase A/C:</h3>
            <img src="assets/av.png" alt="">

            <h3>2.Select the month of process Document:</h3>
            <img src="assets/monthprocess.png" alt="">

            <h3>3.Select the Voucher Date:</h3>
            <img src="assets/voucherdate.png" alt="">

            <h3>4.Processed Invoice Entry Verify:</h3>
            <p>You will see all the entries for this date displayed. Carefully review the entries to ensure all necessary invoices have been processed correctly.</p>
            <img src="assets/invoiceverify.png" alt="">


           </div>



</div>


         <div id="faqs" class="section">
            <h2>3.FAQs</h2>
            
           
             <h3>1.Can I process scanned documents?</h3>
             <p>Yes, AccuVelocity can extract data from scanned documents. However, extraction quality depends on the quality of the document.</p>

             <h3>2.Will the system work if my document is protected by password?</h3>
             <p>No, it will not work if your document is protected by password. For assistance, contact support at <a href="<EMAIL>"><EMAIL></a></p>

             <h3>3.How can I suggest new features or improvements?</h3>
             <p>For suggesting new features, please contact support at <a href="<EMAIL>"><EMAIL></a></p>

             <h3>4.How do I report a problem with the extraction?</h3>
             <p>For any issues, including error messages or incorrect outputs, please contact support at <a href="<EMAIL>"><EMAIL></a></p>

             <h3>5.What is the approximate time taken to process or extract one document (pdf/word/jpg/png)?</h3>
             <p>Document processing times vary based on size and number of pages. Typically, it takes 30 seconds to 3 minutes. Heavy server loads may cause delays.</p>





        </div>


        <div id="troubleshoot" class="section">

            <h2>4.Troubleshooting</h2>
            <p>Watch this troubleshooting guide:</p>
            <iframe src="https://www.youtube.com/embed/c0Ui8i37dAk" title="Troubleshooting Guide" allowfullscreen></iframe>
            <p><a href="https://youtu.be/c0Ui8i37dAk" target="_blank">Watch on YouTube</a></p>

        </div>


        <div id="support" class="section">
            <h2>5.Support</h2>
            <p>If you encounter any issues that you cannot resolve, contact our support team at <a href="<EMAIL>"><EMAIL></a>. Provide a detailed description of your problem, including any error messages and screenshots if applicable.
                Our resolute support team is available to assist you with any questions or issues you may have. You can reach us through the following channels:
            </p>
            <p> • <span style="font-weight: 600;">Email</span> : 
                <a href="<EMAIL>"><EMAIL></a>
               
               
            </p>
            <p> • <span style="font-weight: 600;">Phone</span> : +91 98989 92435
               

            </p>
        </div>

        <div id="conclusion" class="section">
            <h2>6.Conclusion</h2>
            <p>AccuVelocity is an advanced tool designed to optimize velocity measurements and enhance data accuracy. 
                By adhering to this user manual, you can fully leverage its features and ensure peak performance.
                 For any further inquiries or assistance please feel free to contact our support team.
            </p>
           

            </p>
        </div>

</div>



    <script>
        // function toggleSubmenu(id) {
        //   var submenu = document.getElementById(id);
        //   if (submenu.style.display === "none") {
        //     submenu.style.display = "block";
        //   } else {
        //     submenu.style.display = "none";
        //   }
        // }
      </script>
</body>
</html>

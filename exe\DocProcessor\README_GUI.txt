=====================================================================
AccuVelocity Document Processor - GUI Application Guide
=====================================================================

OVERVIEW:
---------
The AccuVelocity Document Processor GUI application provides a user-friendly
interface to all the functionality of the AccuVelocity system. This application
allows you to process documents, export data, and perform various utility
operations without needing to use command-line arguments.

INSTALLATION:
------------
1. Run the "CreateDesktopShortcut.bat" file in the Resources folder to create
   a desktop shortcut for easy access.
2. Double-click the shortcut or the AccuVelocity.exe file to launch the application.

FEATURES:
--------
The application is organized into three main tabs:

1. Document Processing Tab:
   - Select documents to process using the Browse button
   - Choose the appropriate Tally voucher type
   - Enable options like Multiple Vendor and Stock Item Export
   - Click "Process Documents" to start processing

2. Export Tab:
   - Select the type of data to export
   - Click "Export Data" to perform the export operation

3. Utilities Tab:
   - Process XML files or directories
   - Generate activity reports
   - Run system diagnostics

USAGE TIPS:
----------
- For document processing, you can select multiple files at once
- The application will automatically handle file paths and command-line arguments
- Progress windows will appear to show the status of operations
- Error messages will be displayed if any issues occur

TROUBLESHOOTING:
--------------
If you encounter any issues:
1. Check the log files in the Logs directory
2. Make sure your license is valid
3. Verify that Tally is properly configured
4. Contact <NAME_EMAIL>

=====================================================================

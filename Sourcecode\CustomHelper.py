import sys
import os
import re
from urllib.parse import urljoin, urlparse
import zlib
sys.path.append(".")
sys.path.append(os.path.abspath(os.path.dirname(__file__)))
import os
from CustomLogger import CLogger
from CustomException import InvalidFilePathError, NoFilesProvidedError, UnsupportedFileFormatError
import hashlib
import jwt
from cryptography.fernet import Fernet # type: ignore
from PopupWindow import CPopupWindow
from myFileHelper import CFileHelper
import traceback
import requests
import json
from datetime import datetime
import httpx
import zipfile
from EncryptResourceConfig import CReadDeveloperConfig
import EmailUtils
import uuid
import socket
# Secret key for encoding and decoding the JWT
import base64
import xml.etree.ElementTree as ET
import pandas as pd
from typing import Tuple
from decimal import Decimal, ROUND_HALF_UP, ROUND_UP, ROUND_CEILING
import time
import psutil
import speedtest
from urllib.parse import urlparse
import pefile
import glob
import subprocess
import shutil
from collections import defaultdict
from urllib.parse import urlparse, urlunparse


from PyQt5.QtWidgets import QApplication
import sys

SECRET_KEY = base64.urlsafe_b64encode(b'ea7634b4c4b4c23a42218d4b0c814869')

class CGeneralHelper:

    @staticmethod
    def MSGetDeveloperConfigFileName():
        # Get the resource directory path
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        
        def file_exists(filename: str) -> bool:
            return os.path.isfile(os.path.join(strResourceDirpath, filename))
        
        # Get device name
        device_name = socket.gethostname()

        # Check hostname first (most reliable)
        if device_name == 'TALLY-SER-2':
            if file_exists("DeveloperConfig_Server2.json"):
                return "DeveloperConfig_Server2.json"

        # If hostname doesn't match, check IP addresses
        try:
            # Get all IPv4 addresses
            import psutil
            local_ips = []
            addrs = psutil.net_if_addrs()
            for iface, addr_list in addrs.items():
                for addr in addr_list:
                    if addr.family == socket.AF_INET and not addr.address.startswith("127."):
                        local_ips.append(addr.address)

            # Check for specific IPs
            for ip in local_ips:
                if ip == "************" and file_exists("DeveloperConfig_Server2.json"):
                    return "DeveloperConfig_Server2.json"
                elif ip == "************" and file_exists("DeveloperConfig - RDPGPU.json"):
                    return "DeveloperConfig - RDPGPU.json"
                elif ip == "************" and file_exists("DeveloperConfig.json"):
                    return "DeveloperConfig.json"
        except Exception:
            # Fallback to the original method if there's an error
            pass

        # Final fallback using gethostbyname
        try:
            ip_address = socket.gethostbyname(device_name)
            if ip_address.split('.')[-1] == '19' and file_exists("DeveloperConfig_Server2.json"):
                return "DeveloperConfig_Server2.json"
            elif ip_address.split('.')[-1] == '15' and file_exists("DeveloperConfig - RDPGPU.json"):
                return "DeveloperConfig - RDPGPU.json"
        except Exception:
            pass

        # Default fallback
        return "DeveloperConfig.json"

    @staticmethod
    def MSGetUserConfigFileName():
        # Get the resource directory path
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        
        def file_exists(filename: str) -> bool:
            return os.path.isfile(os.path.join(strResourceDirpath, filename))
        
        # Get device name
        device_name = socket.gethostname()

        # Check hostname first (most reliable)
        if device_name == 'TALLY-SER-2':
            if file_exists("UserConfig_Server2.json"):
                return "UserConfig_Server2.json"

        # If hostname doesn't match, check IP addresses
        try:
            # Get all IPv4 addresses
            import psutil
            local_ips = []
            addrs = psutil.net_if_addrs()
            for iface, addr_list in addrs.items():
                for addr in addr_list:
                    if addr.family == socket.AF_INET and not addr.address.startswith("127."):
                        local_ips.append(addr.address)

            # Check for specific IPs
            for ip in local_ips:
                if ip == "************" and file_exists("UserConfig_Server2.json"):
                    return "UserConfig_Server2.json"
                elif ip == "************" and file_exists("UserConfig-GPU.json"):
                    return "UserConfig-GPU.json"
        except Exception:
            # Fallback to the original method if there's an error
            pass

        # Final fallback using gethostbyname
        try:
            ip_address = socket.gethostbyname(device_name)
            if ip_address.split('.')[-1] == '19' and file_exists("UserConfig_Server2.json"):
                return "UserConfig_Server2.json"
            elif ip_address.split('.')[-1] == '15' and file_exists("UserConfig-GPU.json"):
                return "UserConfig-GPU.json"
        except Exception:
            pass

        # Default fallback
        return "UserConfig.json"


    @staticmethod
    def MSGetResourceDirectory():
        try:
            exe_dir = CFileHelper._get_executable_directory()
            return os.path.join(exe_dir, "Resources")
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to get resource directory, Error: {e}")
            CLogger.MCWriteLog("debug", f"{traceback.format_exc()}")
            return None

    @staticmethod
    def MSReadJson(strJsonFilePath):
        try:
            if os.path.isfile(strJsonFilePath):
                with open(strJsonFilePath, "r") as f:
                    dictJsonData = json.load(f)
                    CLogger.MCWriteLog("info", f"File read successfully: {strJsonFilePath}.")
                    return dictJsonData
            else:
                CLogger.MCWriteLog("error", f"File not found: {strJsonFilePath}.")
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to read file {strJsonFilePath}, Error: {e}")
            CLogger.MCWriteLog("debug", f"{traceback.format_exc()}")
            return None

    @staticmethod
    def MSExtractZipFile(zip_file_path, extract_dir_path):
        """
        Extracts files from a ZIP archive and returns a list of file locations.

        Args:
            zip_file_path (str): Path to the ZIP file.
            extract_dir_path (str): Directory path to store extracted files.

        Returns:
            list: List of full paths to the extracted files.
        """
        if not os.path.exists(zip_file_path):
            raise FileNotFoundError(f"The ZIP file '{zip_file_path}' does not exist.")

        if not os.path.exists(extract_dir_path):
            os.makedirs(extract_dir_path)  # Create the directory if it doesn't exist

        extracted_files = []

        with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
            zip_ref.extractall(extract_dir_path)
            extracted_files = [os.path.join(extract_dir_path, file) for file in zip_ref.namelist()]

        return extracted_files

    @staticmethod
    def MSExtractBaseUrl(url: str, bRaiseError: bool = True) -> str:
        """
        Input:

            url (str): A full URL string, e.g., "http://************:8055/IndianInvTally/process_doc"

        Output:

            str: Base URL extracted from the input, e.g., "http://************:8055/"

        Purpose:

            To extract the base part of a given URL using regular expressions.
            The base URL includes the scheme, IP/domain, and port with a trailing slash.

        Example:

            url = "http://************:8055/IndianInvTally/process_doc"
            base_url = CGeneralHelper.MSExtractBaseUrl(url)
            print(base_url)  # Output: "http://************:8055/"
        """
        match = re.match(r"^(https?://[^/]+/)", url)
        if match:
            return match.group(1)
        else:
            if bRaiseError:
                raise ValueError("Invalid URL format")
            else:
                print("Unabel to Get URL from REGEX")
                return None

    @staticmethod
    def MSGetCurrentFormatTimeStamp(strFormat = "%Y-%m-%d %H:%M:%S"):
        return datetime.now().strftime(strFormat)

class CResoucedataHelper:

    @staticmethod
    def MSGetDeveloperConfig():
        """
        Input:

            None

        Output:

            dict: Dictionary containing the client configuration read from the JSON file.

        Purpose:

            To fetch and return the client configuration by reading the 'DeveloperConfig.json'
            file located in the resource directory.

        Example:

            config = MSGetDeveloperConfig()
            print(config)

        """
        try:
            # Get the resource directory path
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            CLogger.MCWriteLog("info", f"Resource Directory Path: {strResourceDirpath}")

            strDeveloperConfigFileName = CGeneralHelper.MSGetDeveloperConfigFileName()
            strTallyDeveloperDataPath = os.path.join(strResourceDirpath, strDeveloperConfigFileName)
            CLogger.MCWriteLog("info", f"Config file Path: {strTallyDeveloperDataPath}")

            # Read the developer configuration file
            bytesDeveloperConfig = CReadDeveloperConfig.MSDecryptDeveloperConfig(
                file_path=strTallyDeveloperDataPath
            )
            dictDeveloperConfig = json.loads(bytesDeveloperConfig)

            if not dictDeveloperConfig:
                CLogger.MCWriteLog("error", "Failed to decrypt DeveloperConfig.json.")
                return

            return dictDeveloperConfig

        except Exception as e:
            # Log the error if something goes wrong
            CLogger.MCWriteLog("error", f"An error occurred while fetching the developer config: {str(e)}")
            return None

    @staticmethod
    def MSGetUserConfig():
        """
        Input:

            None

        Output:

            dict: Dictionary containing the client configuration read from the JSON file.

        Purpose:

            To fetch and return the client configuration by reading the 'UserConfig.json'
            file located in the resource directory.

        Example:

            config = MSGetUserConfig()
            print(config)

        """
        try:
            # Get the resource directory path
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            CLogger.MCWriteLog("info", f"Resource Directory Path: {strResourceDirpath}")

            # Determine the user config file path
            strUserConfigFileName = CGeneralHelper.MSGetUserConfigFileName()
            strTallyUserDataPath = os.path.join(strResourceDirpath, strUserConfigFileName)
            CLogger.MCWriteLog("info", f"Config file Path: {strTallyUserDataPath}")

            # Read the configuration file
            dictUserConfig = CGeneralHelper.MSReadJson(strTallyUserDataPath)
            if not dictUserConfig:
                CLogger.MCWriteLog("error", f"Failed to read user config file {strTallyUserDataPath}.")
                return

            return dictUserConfig

        except Exception as e:
            # Log the error if something goes wrong
            CLogger.MCWriteLog("error", f"An error occurred while fetching the user config: {str(e)}")
            return

    @staticmethod
    def MSGetUserConfigLocation():
        # Get the resource directory path
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        CLogger.MCWriteLog("info", f"Resource Directory Path: {strResourceDirpath}")

        # Determine the user config file path
        strUserConfigFileName = CGeneralHelper.MSGetUserConfigFileName()
        strTallyUserDataPath = os.path.join(strResourceDirpath, strUserConfigFileName)
        return strTallyUserDataPath

    @staticmethod
    def MSGetAccuvelocityConfig():
        try:
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            CLogger.MCWriteLog("info", f"Resource Directory Path: {strResourceDirpath}")

            strVersionInfoFileName = "AccuveloctyConfig.json"

            strVersionInfoFile = os.path.join(strResourceDirpath, strVersionInfoFileName)
            CLogger.MCWriteLog("info", f"Config file Path: {strVersionInfoFile}")

            # Check if file exists
            if not os.path.exists(strVersionInfoFile):
                CLogger.MCWriteLog("error", f"AccuveloctyConfig.json file not found at {strVersionInfoFile}")
                print(f"Error: AccuveloctyConfig.json file not found at {strVersionInfoFile}")
                return {
                    "Exe_version": "2.1",
                    "Exe_ReleaseDate": "2025-05-03",
                    "Tdl_version": "8.0",
                    "Tdl_ReleaseDate": "2025-03-29",
                    "worker": 2,
                    "apiEndpoints": [],
                    "Bulk_Document_Processing_Trigger_PageCount":40
                }

            # Read and parse the file
            try:
                with open(strVersionInfoFile, "r") as file:
                    version_info = json.load(file)

                # Validate required fields
                if "apiEndpoints" not in version_info:
                    CLogger.MCWriteLog("warning", "apiEndpoints field missing in AccuveloctyConfig.json")
                    version_info["apiEndpoints"] = []

                if "worker" not in version_info:
                    CLogger.MCWriteLog("warning", "worker field missing in AccuveloctyConfig.json")
                    version_info["worker"] = 2

                return version_info
            except json.JSONDecodeError as e:
                CLogger.MCWriteLog("error", f"Invalid JSON format in AccuveloctyConfig.json: {str(e)}")
                print(f"Error: Invalid JSON format in AccuveloctyConfig.json: {str(e)}")
                # Return default config
                return {
                    "Exe_version": "2.1",
                    "Exe_ReleaseDate": "2025-05-03",
                    "Tdl_version": "8.0",
                    "Tdl_ReleaseDate": "2025-03-29",
                    "worker": 2,
                    "apiEndpoints": [],
                    "Bulk_Document_Processing_Trigger_PageCount":40
                }
        except Exception as e:
            # Log the error if something goes wrong
            CLogger.MCWriteLog("error", f"An error occurred while fetching the AccuVelocity config: {str(e)}")
            print(f"Error: Failed to read AccuveloctyConfig.json: {str(e)}")
            # Return default config
            return {
                "Exe_version": "2.1",
                "Exe_ReleaseDate": "2025-05-03",
                "Tdl_version": "8.0",
                "Tdl_ReleaseDate": "2025-03-29",
                "worker": 2,
                "apiEndpoints": [],
                "Bulk_Document_Processing_Trigger_PageCount":40
            }





class CArgumentParser:

    @staticmethod
    def MSGetValidFilePathFromArgument(strCommand):
        """Parses and validates file paths from the input command string."""
        lsValidFilePaths = []
        try:

            # Strip, filter, and convert to absolute paths
            lsStrFilePaths = [
                                os.path.abspath(strFilePath.strip())
                                for strFilePath in strCommand.split(';')
                                if strFilePath.strip()
            ]

            if not lsStrFilePaths:
                CLogger.MCWriteLog("error", "No file paths were provided. Please provide valid file paths separated by semicolons.")
                raise NoFilesProvidedError

            for strFilePath in lsStrFilePaths:
                if os.path.isfile(strFilePath):
                    CLogger.MCWriteLog("info", f"Found valid file path: {strFilePath}")
                    lsValidFilePaths.append(strFilePath)
                else:
                    CLogger.MCWriteLog("error", f"Invalid file path: {strFilePath}. Please ensure the file exists and try again.")
                    raise InvalidFilePathError(strFilePath=strFilePath)

            CLogger.MCWriteLog("info", f"Provided Files : {lsValidFilePaths}.")

        except (NoFilesProvidedError, InvalidFilePathError) as ce:
            CLogger.MCWriteLog("error", f"Error parsing file paths: {str(ce)}")
            raise ce
        except Exception as e:
            CLogger.MCWriteLog("error", f"Error parsing file paths: {str(e)}")
            CLogger.MCWriteLog("debug", f"{str(traceback.format_exc())}")

            raise ValueError("There was an issue with the file paths provided. Please check the file paths and try again.")

        return lsValidFilePaths


    @staticmethod
    def MSGetExportType(objReceivedArgs):
        """
        This method determines the export type based on the arguments received.
        """
        try:
            if objReceivedArgs.export_all:
                return "ExportAll"
            elif objReceivedArgs.export_abstract:
                return "ExportAbstract"
            elif objReceivedArgs.export_detailed:
                return "ExportDetailed"
            elif objReceivedArgs.export_filtered_stock_item:
                return "ExportFilteredStockItem"
            elif objReceivedArgs.export_daybook_data:
                return "ExportDayBookData"
            else:
                return None
        except Exception as e:
            CLogger.MCWriteLog("error", f"Error determining export type: {str(e)}")
            return None

    @staticmethod
    def MSCheck_url(url):
        """
    Check if a URL is working by sending a GET request and verifying the response.

    Args:
        url (str): The URL to check.

    Returns:
        bool: True if the URL responds with status 200 and {"Hello": "World"}, False otherwise.
    """
        try:
            response = requests.get(url, timeout=5)
            return response.status_code == 200 and response.json() == {"Hello": "World"}
        except (requests.RequestException, ValueError):
            return False

    @staticmethod
    def MSGet_working_endpoint(original_url, alternative_bases):
        """
        Determine a working API endpoint by checking the original base URL and alternative base URLs.

        Args:
            original_url (str): The original endpoint URL (e.g., "http://122.170.3.105:8034/api/tally/Export").
            alternative_bases (list): List of alternative base URLs (e.g., ["http://103.240.35.194:8034/", ...]).

        Returns:
            str: The working endpoint URL constructed with a working base and the original path.

        Raises:
            Exception: If no working base URL is found.
        """
        try:
            # Validate inputs
            if not original_url:
                CLogger.MCWriteLog("error", "Original URL is empty or None")
                raise ValueError("Original URL is required")

            if not alternative_bases or not isinstance(alternative_bases, list):
                CLogger.MCWriteLog("warning", "Alternative bases list is empty or not a list")
                # Continue with just the original URL
                alternative_bases = []

            # Parse the original URL to extract components
            try:
                parsed = urlparse(original_url)
                original_base = f"{parsed.scheme}://{parsed.netloc}/"
                original_path = parsed.path

                # Log the parsed components
                CLogger.MCWriteLog("info", f"Original URL: {original_url}")
                CLogger.MCWriteLog("info", f"Parsed base: {original_base}")
                CLogger.MCWriteLog("info", f"Parsed path: {original_path}")
            except Exception as parse_error:
                CLogger.MCWriteLog("error", f"Failed to parse URL '{original_url}': {parse_error}")
                raise ValueError(f"Invalid URL format: {original_url}")

            # Check if the original base URL is working
            try:
                if original_base and CArgumentParser.MSCheck_url(original_base):
                    CLogger.MCWriteLog("info", f"Original base URL is working: {original_base}")
                    return original_url
            except Exception as check_error:
                CLogger.MCWriteLog("warning", f"Error checking original base URL: {check_error}")
                # Continue to alternative bases

            # Check alternative base URLs
            working_bases = []
            for alt_base in alternative_bases:
                try:
                    if alt_base and CArgumentParser.MSCheck_url(alt_base):
                        CLogger.MCWriteLog("info", f"Alternative base URL is working: {alt_base}")
                        working_bases.append(alt_base)
                except Exception as alt_error:
                    CLogger.MCWriteLog("warning", f"Error checking alternative base URL '{alt_base}': {alt_error}")
                    continue

            # Use the first working alternative base
            if working_bases:
                result_url = urljoin(working_bases[0], original_path)
                CLogger.MCWriteLog("info", f"Using working endpoint: {result_url}")
                return result_url

            # If we get here, no working base was found
            CLogger.MCWriteLog("error", "No working base URL found among all alternatives")
            raise Exception("All servers are currently unavailable. Our team is working to resolve the issue. For support, please visit Accuvelocity.com.")

        except Exception as e:
            CLogger.MCWriteLog("error", f"Error in MSGet_working_endpoint: {str(e)}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            print(f"Error finding working endpoint: {str(e)}")
            raise Exception("All servers are currently unavailable. Our team is working to resolve the issue. For support, please visit Accuvelocity.com.")

class CDocument:

    def __init__(self, file_path):
        self.file_path = file_path
        self.filename = os.path.basename(file_path)
        CLogger.MCWriteLog("info", f"Initializing document for file: {self.filename}")

        self.content_type = self.get_content_type()
        CLogger.MCWriteLog("info", f"Detected content type: {self.content_type}")

        # Validate content type
        if self.content_type == "application/octet-stream":
            CLogger.MCWriteLog("error", f"Unsupported file format for {self.file_path}")
            raise UnsupportedFileFormatError(self.file_path, self.content_type)

        self._data = self.read_file()
        self.checksum = self.calculate_checksum()
        CLogger.MCWriteLog("info", f"Document initialized successfully: {self.filename}")


    def read_file(self):
        """Reads the file data into memory."""
        try:
            with open(self.file_path, 'rb') as f:
                data = f.read()
                CLogger.MCWriteLog("info", f"File read successfully: {self.filename}, Size: {len(data)} bytes")
                return data
        except Exception as e:
            raise IOError(f"Failed to read file {self.file_path}: {e}")

    def calculate_checksum(self):
        """Calculates the file's checksum using MD5."""
        try:
            checksum = hashlib.md5(self._data).hexdigest()
            CLogger.MCWriteLog("info", f"Checksum calculated for {self.filename}: {checksum}")
            return checksum
        except Exception as e:
            raise ValueError(f"Checksum calculation failed: {e}")

    def calculate_checksum_from_file(file_path: str) -> str:
        """
        Calculates the MD5 checksum of a file using its path.

        Args:
            file_path (str): Path to the file

        Returns:
            str: The calculated MD5 checksum
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            checksum = hash_md5.hexdigest()
            CLogger.MCWriteLog("info", f"Checksum calculated for {file_path}: {checksum}")
            return checksum
        except Exception as e:
            raise ValueError(f"Checksum calculation failed for {file_path}: {e}")

    @property
    def size(self):
        """Returns the file size in bytes."""
        file_size = len(self._data)
        CLogger.MCWriteLog("info", f"File size for {self.filename}: {file_size} bytes")
        return file_size


    def get_content_type(self):
        """Determines the content type based on file extension."""
        extension = self.get_extension()
        mime_types = {
            ".pdf": "application/pdf",
            ".jpg": "image/jpeg",
            ".png": "image/png",
            ".xls": "application/vnd.ms-excel",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".zip": "application/zip",
            ".rar": "application/vnd.rar",
            ".7z": "application/x-7z-compressed",
            ".xml": "application/xml",  # Added MIME type for XML
            ".csv": "text/csv",          # Added MIME type for CSV
            ".html": "text/html",             # Added MIME type for HTML
            ".htm": "text/html"               # Also add .htm variant
        }
        return mime_types.get(extension, "application/octet-stream")

    def get_extension(self):
        """Extracts the file extension in lowercase."""
        extension = os.path.splitext(self.filename)[1].lower()
        CLogger.MCWriteLog("info", f"File extension for [{self.filename}] is {extension}")
        return extension

    @staticmethod
    def MSCalculateChecksum(file_path):
        """
        Calculate the SHA256 checksum of a file.
        """
        with open(file_path, "rb") as f:
            file_content = f.read()
            return hashlib.md5(file_content).hexdigest()
        return False

class CLicenseHelper:

    @staticmethod
    def MSVerifyLicense(license_file=r"license.lic"):
        try:
            exe_dir = CFileHelper._get_executable_directory()
            license_file = os.path.join(exe_dir, license_file)

            if not os.path.exists(license_file):
                CLogger.MCWriteLog("error", f"License file {license_file} not found")
                raise FileNotFoundError(f"License file {license_file} not found")

            # Load License File
            with open(license_file, "rb") as lic_file:
                encrypted_license = lic_file.read()

            # Decrypt License
            cipher_suite = Fernet(SECRET_KEY)
            token = cipher_suite.decrypt(encrypted_license).decode("utf-8")
            dictLicenseData = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
            dictLicenseData["Token"] = token
            # Decode JWT Token
            # payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
            # stored_mac = payload['mac_address']
            # current_mac = hex(uuid.getnode()).replace("0x", "").upper()

            # if current_mac != stored_mac:
            #     CLogger.MCWriteLog("error", f"License verification failed, License is already in use by different device.")
            #     raise Exception("License verification failed: License is already in use.")
            CLogger.MCWriteLog("info", f"License verification succeeded.")
            return  dictLicenseData

        except Exception as e:
            CLogger.MCWriteLog("error", f"License verification failed, Error: {e}")
            CLogger.MCWriteLog("debug", f"{traceback.format_exc()}")

            CPopupWindow(message="License verification failed", message_type="error")
            raise e

class CGeneralInfoHelper:

    @staticmethod
    def MSReadFile(strFilePath):
        try:
            if os.path.isfile(strFilePath):
                with open(strFilePath, "r") as f:
                    strFileContent = f.read()
                    CLogger.MCWriteLog("info", f"File read successfully: {strFilePath}.")
                    return strFileContent
            else:
                CLogger.MCWriteLog("error", f"File not found: {strFilePath}.")
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to read file {strFilePath}, Error: {e}")
            CLogger.MCWriteLog("debug", f"{traceback.format_exc()}")


    # @staticmethod
    # def MSGetVersionDetails(strVersionFilePath="version.txt"):
    #     try:
    #         exe_dir = CFileHelper._get_executable_directory()
    #         strVersionFilePath = os.path.join(exe_dir, "Resources", strVersionFilePath)

    #         CLogger.MCWriteLog("info", f"Version File Path: {strVersionFilePath}")

    #         strFileContent = CGeneralInfoHelper.MSReadFile(strVersionFilePath)
    #         return strFileContent

    #     except Exception as e:
    #         CLogger.MCWriteLog("error", f"Failed to show version details from file {strVersionFilePath}, Error: {e}")
    #         CLogger.MCWriteLog("debug", f"{traceback.format_exc()}")
    #         return "Failed to Fetch current version information, It might be because of corrupted resource file."

    @staticmethod
    def MSGetVersionDetails(strVersionInfoFile=r"Resources\AccuveloctyConfig.json"):
        # Initialize the version_info variable
        version_info = {}

        exe_dir = CFileHelper._get_executable_directory()
        strVersionInfoFile = os.path.join(exe_dir, strVersionInfoFile)

        # Check if the file exists
        if not os.path.exists(strVersionInfoFile):
            raise FileNotFoundError(f"VersionInfo.json file not found at {strVersionInfoFile}")

        # Load the content of the JSON file
        with open(strVersionInfoFile, "r") as file:
            version_info = json.load(file)

        # Extract the Exe_version and Exe_ReleaseDate
        exe_version = version_info.get("Exe_version", "Unknown")
        exe_release_date_str = version_info.get("Exe_ReleaseDate", "Unknown")

        # Convert the Exe_ReleaseDate to the desired format
        try:
            exe_release_date = datetime.strptime(exe_release_date_str, "%Y-%m-%d").strftime("%d/%m/%Y")
        except ValueError:
            exe_release_date = "Invalid Date"

        # Create the return string in the required format
        result_string = f"Note: Use `--help` to explore available options.  \n\nVersion: {exe_version} \nRelease Date: {exe_release_date}  "

        return result_string



class CTallyExportHelper:

    @staticmethod
    def MSSendTallyImportedXMLResToServer(dictInstalledExeDetail, strTallyResponseXMLDir, lsXMLFiles, strToken, strURL):
        """
        Input:

            lsXMLFiles: list
                List of XML file paths to be sent to the AHM server.

            strToken: str
                Authorization token for making API calls to the AHM server.

        Output:

            dict or None:
                Dictionary containing the response from the AHM server if the call succeeds,
                or None if the call fails.

        Purpose:

            To send processed Tally XML response files as a ZIP archive to the AHM server
            and validate the server's response.

        Example:

            lsXMLFiles = ["path/to/response1.xml", "path/to/response2.xml"]
            response = obj.MSSendTallyImportedXMLResToServer(lsXMLFiles, "your_auth_token")
            print(response)

        """
        try:
            # Prepare API request
            headers = {
                "Authorization": f"Bearer {strToken}",
            }

            # Create a zip file and save it in the provided directory
            strCreatedXMLTimestamp = datetime.now().strftime("%d-%m-%Y-%H-%M")
            zip_file_path = os.path.join(strTallyResponseXMLDir, f"TallyImportedResponse_{strCreatedXMLTimestamp}.zip")

            # Ensure all files in lsXMLFiles exist before writing to the zip
            lsValidXMLFiles = [file_path for file_path in lsXMLFiles if os.path.exists(file_path)]

            if not lsValidXMLFiles:
                CLogger.MCWriteLog(
                    "DEBUG",
                    f"Tally Imported XML Response Files: {lsXMLFiles}. Valid Files After Path Existence Validation: {lsValidXMLFiles}."
                )
                return None

            with zipfile.ZipFile(zip_file_path, 'w') as zipf:
                for file_path in lsValidXMLFiles:
                    zipf.write(file_path, os.path.basename(file_path))

            CLogger.MCWriteLog("info", f"Zip file created successfully at {zip_file_path}")

            # Compute checksum for the zip file
            zip_checksum = CDocument.MSCalculateChecksum(zip_file_path)
            CLogger.MCWriteLog("info", f"Checksum for zip file: {zip_checksum}")

            # Read the zip file content
            with open(zip_file_path, 'rb') as zipf:
                files = [("documents", (f"TallyImportedResponse_{strCreatedXMLTimestamp}.zip", zipf.read(), "application/zip"))]

            # Prepare data for checksum and comments

            strClientDetail = json.dumps(dictInstalledExeDetail)  # Serialize to JSON string
            data = {"checksums": zip_checksum, "strClientDetail": strClientDetail}
            params = {}

            # Will wait for 10 minutes for server response
            with httpx.Client(timeout=600) as client:
                response = client.post(
                    strURL,
                    headers=headers,
                    params=params,
                    files=files,
                    data=data
                )

            if response.status_code == 200:
                data = response.json()
                CLogger.MCWriteLog("info", f"CLIENT Tally IMPORTED RESPONSE API call completed successfully with response: {response.json()}.")
                return data
            else:
                CLogger.MCWriteLog("error", f"CLIENT Tally IMPORTED RESPONSE API call failed with status code {response.status_code} and response: {response.text}.")
                return None

        except httpx.RequestError as e:
            CLogger.MCWriteLog("error", f"API request failed: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")

        except Exception as e:
            CLogger.MCWriteLog("error", f"API request failed: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")

    @staticmethod
    def MSExportNSaveTallyData(strExportType, strToken,strTallyRequestDataPath="Exports",lsalternative_bases=[]):
        try:
            # Process and log file objects
            bIsExported = False
            CLogger.MCWriteLog("info", "Tally Data Export Requested.")

            # Get the resource directory path
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            CLogger.MCWriteLog("info", f"Resource Directory Path: {strResourceDirpath}")

            # Decrypt DeveloperConfig.json file
            strDeveloperConfigFileName = CGeneralHelper.MSGetDeveloperConfigFileName()
            strTallyDeveloperDataPath = os.path.join(strResourceDirpath, strDeveloperConfigFileName)

            # Determine the user config file path
            strUserConfigFileName = CGeneralHelper.MSGetUserConfigFileName()
            strTallyUserDataPath = os.path.join(strResourceDirpath, strUserConfigFileName)

            CLogger.MCWriteLog("info", f"Developer Config file Path: {strTallyDeveloperDataPath}")
            CLogger.MCWriteLog("info", f"User Config file Path: {strTallyUserDataPath}")

            # Read the user configuration file
            dictUserConfig = CGeneralHelper.MSReadJson(strTallyUserDataPath)
            if not dictUserConfig:
                CLogger.MCWriteLog("error", f"Failed to read request config file {strTallyUserDataPath}.")
                return

            # Read the developer configuration file
            try:
                bytesDeveloperConfig = CReadDeveloperConfig.MSDecryptDeveloperConfig(file_path=strTallyDeveloperDataPath)
                dictDeveloperConfig = json.loads(bytesDeveloperConfig)
                if not dictDeveloperConfig:
                    CLogger.MCWriteLog("error", "Failed to decrypt DeveloperConfig.json.")
                    return

            except Exception as e:
                CLogger.MCWriteLog("error", f"Exception while decrypting DeveloperConfig.json: {str(e)}")
                # Optionally raise the exception further if needed or return
                return


            # Extract configuration values
            strTallyServerUrl = dictUserConfig.get("ExportRequest", {}).get("ReqUrl")
            strDayBookFilePath = dictUserConfig.get("ExportRequest", {}).get("AbhinavDayBookPath")
            strDetailedReqFilePath = dictUserConfig.get("ExportRequest", {}).get("DetailedReqFilePath")
            strAbstractReqFilePath = dictUserConfig.get("ExportRequest", {}).get("AbstractReqFilePath")
            strFilteredGetItemsReqFilePath = dictUserConfig.get("ExportRequest", {}).get("FilteredGetItemsReqFilePath")
            strExportDataDirectoryPath = dictUserConfig.get("ExportRequest", {}).get("ExportDataDirectoryPath")
            

            if not (strTallyServerUrl and strDetailedReqFilePath and strAbstractReqFilePath and strExportDataDirectoryPath and strFilteredGetItemsReqFilePath and strDayBookFilePath):
                CLogger.MCWriteLog("error", f"Failed to read request config from data {dictUserConfig}.")
                return

            # Create today's date folder
            today_date = datetime.now().strftime("%d_%m_%Y")
            date_folder_path = os.path.join(strResourceDirpath, strExportDataDirectoryPath, today_date)
            os.makedirs(date_folder_path, exist_ok=True)
            CLogger.MCWriteLog("info", f"Date folder created: {date_folder_path}")

            # Generate file names with timestamp
            current_time = datetime.now().strftime("%H_%M_%S")
            export_daybook_filename = f"Daybook_{today_date}_{current_time}.xml"
            detailed_export_filename = f"DetailedExport_{today_date}_{current_time}.xml"
            abstract_export_filename = f"AbstractExport_{today_date}_{current_time}.xml"
            filtered_export_filename = f"FilteredStockItemExport_{today_date}_{current_time}.xml"
            # Create full file paths
            strDetailedExportFilePath = os.path.join(date_folder_path, detailed_export_filename)
            strAbstractExportFilePath = os.path.join(date_folder_path, abstract_export_filename)
            strFilteredStockItemExportFilePath = os.path.join(date_folder_path, filtered_export_filename)
            strDailyDataExportFilePath = os.path.join(date_folder_path, export_daybook_filename)
            CLogger.MCWriteLog("info", f"Detailed Export File Path: {strDetailedExportFilePath}")
            CLogger.MCWriteLog("info", f"Abstract Export File Path: {strAbstractExportFilePath}")

            # Update request file paths
            strDetailedReqFilePath = os.path.join(strResourceDirpath, strDetailedReqFilePath)
            strAbstractReqFilePath = os.path.join(strResourceDirpath, strAbstractReqFilePath)
            strFilteredGetItemsReqFilePath = os.path.join(strResourceDirpath, strFilteredGetItemsReqFilePath)
            strDailyDataReqFilePath = os.path.join(strResourceDirpath, strDayBookFilePath)
            lsComments = []

            # Perform export based on type
            if strExportType:
                if strExportType == "ExportAll":
                    CLogger.MCWriteLog("info", "Exporting both Detailed and Abstract Tally Data.")
                    strExportDetailedDataComment = CTallyExportHelper.MSExportTallyData(
                        strRequestXMLPath=strDetailedReqFilePath,
                        strResponseXMLPath=strDetailedExportFilePath,
                        url=strTallyServerUrl
                    )
                    strExportedAbstractDataComment = CTallyExportHelper.MSExportTallyData(
                        strRequestXMLPath=strAbstractReqFilePath,
                        strResponseXMLPath=strAbstractExportFilePath,
                        url=strTallyServerUrl
                    )
                    lsComments.extend([strExportDetailedDataComment, strExportedAbstractDataComment])

                elif strExportType == "ExportAbstract":
                    CLogger.MCWriteLog("info", "Exporting Abstract Tally Data only.")
                    strExportedAbstractDataComment = CTallyExportHelper.MSExportTallyData(
                        strRequestXMLPath=strAbstractReqFilePath,
                        strResponseXMLPath=strAbstractExportFilePath,
                        url=strTallyServerUrl
                    )
                    lsComments.append(strExportedAbstractDataComment)

                elif strExportType == "ExportDetailed":
                    CLogger.MCWriteLog("info", "Exporting Detailed Tally Data only.")
                    strExportDetailedDataComment = CTallyExportHelper.MSExportTallyData(
                        strRequestXMLPath=strDetailedReqFilePath,
                        strResponseXMLPath=strDetailedExportFilePath,
                        url=strTallyServerUrl
                    )
                    lsComments.append(strExportDetailedDataComment)

                elif strExportType == "ExportFilteredStockItem":
                    CLogger.MCWriteLog("info", "Exporting Detailed Tally Data only.")
                    strExportDetailedDataComment = CTallyExportHelper.MSExportTallyData(
                        strRequestXMLPath=strFilteredGetItemsReqFilePath,
                        strResponseXMLPath=strFilteredStockItemExportFilePath,
                        url=strTallyServerUrl
                    )
                    lsComments.append(strExportDetailedDataComment)
                
                elif strExportType == "ExportDayBookData":
                    CLogger.MCWriteLog("info", "Exporting Day Book Data.")
                    CTallyExportHelper.MSUpdateDayBookXMLReqDates(strDailyDataReqFilePath)
                    
                    strExportDetailedDataComment = CTallyExportHelper.MSExportTallyData(
                        strRequestXMLPath=strDailyDataReqFilePath,
                        strResponseXMLPath=strDailyDataExportFilePath,
                        url=strTallyServerUrl
                    )
                    if strExportDetailedDataComment.startswith("Error"):
                        print(strExportDetailedDataComment)
                        raise Exception(strExportDetailedDataComment)
                    
                    lsComments.append(strExportDetailedDataComment)
                    update_daybook_database(strToken = strToken, strReportFilePath = strDailyDataExportFilePath)
                    bIsExported = True
                else:
                    CLogger.MCWriteLog("error", f"Invalid export type: {strExportType}")
                    return


                lsDocumentObject = []
                for file_path in [strDetailedExportFilePath, strAbstractExportFilePath, strFilteredStockItemExportFilePath]:
                    if os.path.exists(file_path):
                        try:
                            objDocument = CDocument(file_path)
                            lsDocumentObject.append(objDocument)
                            bIsExported = True
                            CLogger.MCWriteLog("info", f"Processed file: {file_path}")
                        except UnsupportedFileFormatError:
                            CLogger.MCWriteLog("error", f"Unsupported file format for {file_path}")
                        except Exception as e:
                            CLogger.MCWriteLog("error", f"Error processing file {file_path}: {e}")
                            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")

                # Send export status
                strExportURL = dictDeveloperConfig.get("ExportRequest", {}).get("APIEndPoints", {}).get("ExportUrl", "")
                try:
                    strExportURL=str(CArgumentParser.MSGet_working_endpoint(strExportURL,lsalternative_bases))
                    CLogger.MCWriteLog("info",f"Working URL selected: {strExportURL}")
                except Exception as e:
                    CLogger.MCWriteLog("error",f"Error: {str(e)}")

                # Send Email Notification if no items are exported
                if not bIsExported:
                    try:

                        dictEmailConfig = dictDeveloperConfig.get("EmailConfig")
                        strEmailFrom = dictEmailConfig.get("strMailFrom")
                        strEmailPassword = dictEmailConfig.get("strPassword")
                        strEmailSubject = dictEmailConfig.get("strSubject")
                        lsEmailTo = dictEmailConfig.get("lsMailTo")
                        lsEmailToCC = dictEmailConfig.get("lsMailToCC")

                        strEmailServer = dictEmailConfig.get("strServer")
                        iEmailPort = dictEmailConfig.get("intPort")

                        dictLicenseInfo = CLicenseHelper.MSVerifyLicense()
                        strReceiverName = dictLicenseInfo.get("name")

                        strVersionInfoFile = dictUserConfig.get("ExportRequest",{}).get("VersionInfo")
                        strExePath = CFileHelper._get_executable_directory()
                        strHTMLTemplatePath = os.path.join(strExePath, r"Resources\Email_Templates\StockItemExportError.html")
                        strVersionInfoFile = os.path.join(strExePath, strVersionInfoFile)
                        dictVersionInfo = CGeneralHelper.MSReadJson(strVersionInfoFile)
                        strAVVersion = dictVersionInfo.get("Exe_version")
                        strAVTDLVersion = dictVersionInfo.get("Tdl_version")
                        strMacAddress = hex(uuid.getnode()).replace("0x", "").upper()

                        EmailUtils.SendTallyNotificationEmail(strReceiverName=strReceiverName, strSubject=strEmailSubject, strMailFrom=strEmailFrom, lsMailTo=lsEmailTo, strServer=strEmailServer, intPort=iEmailPort, strPassword=strEmailPassword, strAVVersion=strAVVersion, strAVTDLVersion=strAVTDLVersion, strMacAddress=strMacAddress, lsCC=lsEmailToCC, htmlTemplatePath= strHTMLTemplatePath)
                    except Exception as e:
                        CLogger.MCWriteLog("error", f"Failed to send the stockitem export Alert, Error: {e}")
                        CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")

                CTallyExportHelper.MSSendTallyExportStatus(
                    lsFilePath=lsDocumentObject,
                    lsComments=lsComments,
                    strToken=strToken,
                    strCurrReqDir=date_folder_path,
                    strExportAPIURL=strExportURL
                )
                CLogger.MCWriteLog("info", "Tally export status sent.")

            else:
                CLogger.MCWriteLog("error", "No export type provided.")
                return

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to export Tally data: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")

    @staticmethod
    def MSUpdateDayBookXMLReqDates(strDailyDataReqFilePath):
        # Update the xml file
        try:
            # Get current date
            current_date = datetime.now()
            current_year = current_date.strftime("%Y")
            current_date_str = current_date.strftime("%Y%m%d")

            # Parse the XML file
            tree = ET.parse(strDailyDataReqFilePath)
            root = tree.getroot()

            # Update SVFROMDATE (change only the year)
            sv_from_date = root.find(".//SVFROMDATE")
            if sv_from_date is not None:
                current_from_date = sv_from_date.text
                if current_from_date and len(current_from_date) == 8:
                    updated_from_date = current_year + current_from_date[4:]
                    sv_from_date.text = updated_from_date
                else:
                    raise ValueError("SVFROMDATE is missing or not in YYYYMMDD format")

            # Update SVTODATE (set to current date)
            sv_to_date = root.find(".//SVTODATE")
            if sv_to_date is not None:
                sv_to_date.text = current_date_str
            else:
                raise ValueError("SVTODATE tag not found in XML")

            # Save the updated XML back to the same file
            tree.write(strDailyDataReqFilePath, encoding="utf-8", xml_declaration=True)
            print(f"Updated XML file saved at: {strDailyDataReqFilePath}")

        except Exception as e:
            print(f"Error processing XML file: {e}")
            raise

    @staticmethod
    def MSSendTallyExportStatus(lsFilePath, lsComments, strToken, strCurrReqDir, strExportAPIURL):
        """
        Calls the process document API.
        """
        try:
            CLogger.MCWriteLog("info", f"Calling API to send export status")

            headers = {
                "Authorization": f"Bearer {strToken}",
            }

            files = []
            checksums = []
            if not lsFilePath:
                raise ValueError("File list is empty. Cannot process.")


            # Extract file paths or content from objects
            extracted_files = []
            for doc in lsFilePath:
                if not hasattr(doc, "file_path"):
                    raise AttributeError("Each object must have a 'file_path' attribute.")
                if not os.path.exists(doc.file_path):
                    raise FileNotFoundError(f"File not found: {doc.file_path}")
                extracted_files.append(doc.file_path)

            # Create a zip file and save it in the provided directory
            zip_file_path = os.path.join(strCurrReqDir, "exported_files.zip")
            with zipfile.ZipFile(zip_file_path, 'w') as zipf:
                for file_path in extracted_files:
                    zipf.write(file_path, os.path.basename(file_path))

            CLogger.MCWriteLog("info", f"Zip file created successfully at {zip_file_path}")

            # Compute checksum for the zip file
            zip_checksum = CDocument.MSCalculateChecksum(zip_file_path)
            CLogger.MCWriteLog("info", f"Checksum for zip file: {zip_checksum}")

            # Read the zip file content
            with open(zip_file_path, 'rb') as zipf:
                files = [("files", ("exported_files.zip", zipf.read(), "application/zip"))]

            # Prepare data for checksum and comments
            data = {"checksums": zip_checksum}
            # params = {"message": ",".join(lsComments)}

            # Prepare API request
            headers = {
                "Authorization": f"Bearer {strToken}",
            }

            # Will wait for 10 minutes for server response
            with httpx.Client(timeout=600) as client:
                response = client.post(
                    strExportAPIURL,
                    headers=headers,
                    # params=params,
                    files=files,
                    data=data
                )

            if response.status_code == 200:
                CLogger.MCWriteLog("info", f"API call completed successfully with response: {response.json()}.")

            else:
                CLogger.MCWriteLog("error", f"API call failed with status code {response.status_code} and response: {response.text}.")


        except httpx.RequestError as e:
            CLogger.MCWriteLog("error", f"API request failed: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")

            # raise ValueError(f"Failed to process documents, Please try again later.")

        except Exception as e:
            CLogger.MCWriteLog("error", f"API request failed: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")


    @staticmethod
    def MSExportTallyData(strRequestXMLPath, strResponseXMLPath="response.xml", url="http://0.0.0.0:9000/"):
        """
        Reads an XML file and sends it to the specified Tally server URL.
        Saves the server response to an XML file.

        Args:
        strRequestXMLPath (str): The path to the XML file to send.
        url (str): The Tally server URL.
        strResponseXMLPath (str): The path where the response XML will be saved.

        Returns:
        str: Confirmation message or error details.
        """
        try:
            # Read XML content from file
            with open(strRequestXMLPath, "r", encoding="utf-8") as file:
                xml_data = file.read()

            # Define headers
            headers = {
                "Content-Type": "text/xml",
                "Cache-Control": "no-cache"
            }

            # Send POST request

            response = requests.post(url, headers=headers, data=xml_data)

            # Save the response to an XML file
            with open(strResponseXMLPath, "w", encoding="utf-8") as file:
                file.write(response.text)

            return f"Response saved to {strResponseXMLPath}"

        except FileNotFoundError:
            return "Error: The specified file was not found."
        except requests.exceptions.RequestException as e:
            return f"Error: Unable to send data to the server. {str(e)}"
        except IOError as e:
            return f"Error: Unable to save the response file. {str(e)}"
        except Exception as GenError:
            return f"Failed to Export the data,  error: {str(GenError)}"

# Define a custom Speedtest class with a User-Agent header
class CustomSpeedtest(speedtest.Speedtest):
    def __init__(self):
        super().__init__()
        self.config['user_agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'

    def get(self, *args, **kwargs):
        headers = {'User-Agent': self.config['user_agent']}
        return super().get(*args, headers=headers, **kwargs)

class CRunDiagnostics:
    def __init__(self):
        try:
            # Load the user-config dict once
            self.user_config = CResoucedataHelper.MSGetUserConfig()
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to load user configuration: {e}")
            self.user_config = {}
        # Initialize AccuVelocity Comment as a list to collect diagnostic messages
        self.accu_velocity_comments = []

    def get_diagnostics_title(self) -> str:
        """
        Fetches the executable version and release date based on the
        path specified in user_config['ExportRequest']['VersionInfo']
        and returns a formatted diagnostics title string.
        """
        CLogger.MCWriteLog("info", "Building diagnostics title from user-config...")
        try:
            config_path = self.user_config.get("ExportRequest", {}).get("VersionInfo", "")
            if not config_path:
                CLogger.MCWriteLog("error", "VersionInfo path not found in UserConfig.json.")
                return "AccuVelocity Diagnostics - Version Unknown - Release Date: Unknown"

            # AccuVelocityConfig.json Read
            with open(config_path, "r") as f:
                accu_config = json.load(f)
            exe_version = accu_config.get("Exe_version", "")
            exe_release_str=accu_config.get("Exe_ReleaseDate", "")

            # Format release date from YYYY-MM-DD to 'Month Day, Year'
            try:
                dt = datetime.strptime(exe_release_str, '%Y-%m-%d')
                formatted_date = dt.strftime('%B %d, %Y')
            except ValueError:
                formatted_date = exe_release_str or 'Unknown'

            # Construct and return title
            title = (
                f"AccuVelocity Diagnostics - Version {exe_version}"
                f" - Release Date: {formatted_date}"
            )
            CLogger.MCWriteLog("info", f"Diagnostics title built: {title}")
            return title

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to fetch diagnostics title: {e}")
            return "AccuVelocity Diagnostics - Version Unknown - Release Date: Unknown"

    def check_internet_connectivity(self, timeout: float = 5.0):
        CLogger.MCWriteLog("info", "Checking internet connectivity...")
        try:
            response = requests.get("https://www.google.com", timeout=timeout)
            response_time = response.elapsed.total_seconds() * 1000  # Convert to milliseconds
            CLogger.MCWriteLog("info", f"Internet connectivity: OK ✅ - Response time: {response_time:.2f}ms")

            # Return a detailed success message
            if response_time < 200:
                comment = f"✓ Internet connection is excellent with a response time of {response_time:.0f}ms. Your connection is optimal for all AccuVelocity operations."
            elif response_time < 500:
                comment = f"✓ Internet connection is good with a response time of {response_time:.0f}ms. This provides reliable performance for AccuVelocity services."
            else:
                comment = f"✓ Internet connection is available with a response time of {response_time:.0f}ms. While functional, a faster connection would improve performance."

            return True, comment

        except requests.RequestException as e:
            CLogger.MCWriteLog("error", f"Internet connectivity: FAILED ❌ - {e}")

            # Provide a user-friendly error message based on the type of exception
            if isinstance(e, requests.ConnectionError):
                comment = "Error: Unable to establish an internet connection. Please check your network settings and ensure your device is connected to the internet."
            elif isinstance(e, requests.Timeout):
                comment = "Error: The connection timed out while trying to reach the internet. This may indicate a slow or unstable connection."
            elif isinstance(e, requests.TooManyRedirects):
                comment = "Error: Too many redirects encountered while trying to reach the internet. This may indicate a network configuration issue."
            else:
                comment = f"Error: Internet connectivity check failed. {str(e)}"

            return False, comment

        except Exception as e:
            CLogger.MCWriteLog("error", f"Unexpected error in check_internet_connectivity: {traceback.format_exc()}")
            comment = f"Error: An unexpected issue occurred while checking internet connectivity. Technical details: {str(e)}"
            return False, comment

    def internet_speed_test(self):
        CLogger.MCWriteLog("info", "Running internet speed test...")
        # print("Running internet speed test…")
        try:
            # Initialize the last_speed_test_results attribute if it doesn't exist
            if not hasattr(self, 'last_speed_test_results'):
                self.last_speed_test_results = {}

            st = CustomSpeedtest()
            st.get_best_server()
            download = st.download() / 1e6
            upload = st.upload() / 1e6

            # Store the results for later use in the report
            self.last_speed_test_results = {
                'download': download,
                'upload': upload
            }

            # Evaluate download speed
            download_quality = "poor" if download < 4 else "average" if download < 20 else "good" if download < 60 else "excellent"

            # Evaluate upload speed
            upload_quality = "poor" if upload < 2 else "average" if upload < 10 else "good" if upload < 30 else "excellent"

            # Log the results
            CLogger.MCWriteLog("info", f"Internet speed test: Download: {download:.2f} Mbps ({download_quality}), Upload: {upload:.2f} Mbps ({upload_quality})")
            print(f"Download: {download:.2f} Mbps, Upload: {upload:.2f} Mbps")

            # Determine overall quality and appropriate message
            if download_quality == "poor" and upload_quality == "poor":
                message = "Both your download and upload speeds are poor. 😞"
                comment = f"Your internet connection is slow (Download: {download:.2f} Mbps, Upload: {upload:.2f} Mbps). Please connect to a faster network for optimal performance."
                CLogger.MCWriteLog("warning", "Both download and upload speeds are poor")
                print(message)
                return False, comment
            elif download_quality == "poor":
                message = "Your download speed is poor. 😞"
                comment = f"Your download speed is {download:.2f} Mbps, which is too slow. This may affect your ability to receive data efficiently."
                CLogger.MCWriteLog("warning", f"Poor download speed: {download:.2f} Mbps")
                print(message)
                return False, comment
            elif upload_quality == "poor":
                message = "Your upload speed is poor. 😞"
                comment = f"Your upload speed is {upload:.2f} Mbps, which is too slow. This may affect your ability to send data to our servers."
                CLogger.MCWriteLog("warning", f"Poor upload speed: {upload:.2f} Mbps")
                print(message)
                return False, comment
            else:
                quality = "average" if download_quality == "average" or upload_quality == "average" else "good" if download_quality == "good" or upload_quality == "good" else "excellent"
                emoji = "🙂" if quality == "average" else "👍" if quality == "good" else "🚀"
                message = f"Your internet speed is {quality}! {emoji}"
                CLogger.MCWriteLog("info", f"Internet speed quality: {quality}")
                print(message)
                # Include the speed values in the comment for successful tests
                comment = f"Download: {download:.2f} Mbps, Upload: {upload:.2f} Mbps - Your internet speed is {quality}."
                return True, comment
        except speedtest.SpeedtestException as e:
            CLogger.MCWriteLog("error", f"Speed test failed: {e}")
            print("Speed test failed. ❌")
            comment = f"Speed test failed: {traceback.format_exc()}"
            return False, comment
        except Exception as e:
            CLogger.MCWriteLog("error", f"Unexpected error in internet_speed_test: {e}")
            print("Unexpected error occurred during speed test. ❌")
            comment = f"Unexpected error during internet speed test: {traceback.format_exc()}"
            return False, comment

    def ram_usage_analysis(self):
        CLogger.MCWriteLog("info", "Analyzing RAM usage...")
        try:
            vm = psutil.virtual_memory()
            used = vm.percent
            CLogger.MCWriteLog("info", f"RAM Usage: {used:.2f}%")
            print(f"RAM Usage: {used:.2f}%")
            if used > 85:
                comment = f"Your RAM usage is {used:.2f}%, which is higher than 85%. Please free up your RAM."
                CLogger.MCWriteLog("warning", "High RAM usage detected (>85%).")
                print("⚠️ High RAM usage detected (>85%).")
                return False, comment
            else:
                CLogger.MCWriteLog("info", "RAM usage within normal range.")
                print("✅ RAM usage within normal range.")
                return True, ""
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to analyze RAM usage: {e}")
            print("Failed to analyze RAM usage. ❌")
            comment = f"Failed to analyze RAM usage: {e}"
            return False, comment

    def cpu_usage_analysis(self, interval: float = 4.0) -> tuple[bool, str]:
        """Report CPU usage and classify it."""
        CLogger.MCWriteLog("info", "Analyzing CPU usage...")
        try:
            usage = psutil.cpu_percent(interval=interval)
            if usage < 50:
                status = "Good ✅"
            elif usage < 80:
                status = "Average ⚖️"
            else:
                status = "High ⚠️"
            CLogger.MCWriteLog("info", f"CPU Usage: {usage:.2f}% — {status}")
            print(f"CPU Usage: {usage:.2f}% — {status}")
            if usage > 70:
                comment = f"Your CPU usage is {usage:.2f}%, which is higher than 70%. Consider reducing system load."
                return False, comment
            return True, ""
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to analyze CPU usage: {e}")
            print("Failed to analyze CPU usage. ❌")
            comment = f"Failed to analyze CPU usage: {e}"
            return False, comment

    def disk_usage_analysis(self, path: str = "/") -> tuple[bool, str]:
        """
        Check disk usage on the given path and ensure sufficient space for AccuVelocity.

        AccuVelocity requires at least 20GB of free space for optimal operation.
        """
        CLogger.MCWriteLog("info", f"Analyzing disk usage for path: {path}")
        try:
            du = psutil.disk_usage(path)
            used_percent = du.percent
            free_percent = 100 - used_percent
            free_gb = du.free / (1024**3)  # Convert bytes to GB

            CLogger.MCWriteLog("info", f"Disk '{path}': Used {used_percent:.2f}%, Free {free_percent:.2f}% ({free_gb:.2f} GB)")
            print(f"Disk '{path}': Used {used_percent:.2f}%, Free {free_percent:.2f}% ({free_gb:.2f} GB)")

            # Check if free space is less than 20GB
            if free_gb < 20:
                comment = (f"AccuVelocity requires at least 20GB of free disk space, but only {free_gb:.2f}GB available on {path}. "
                          f"Please free up disk space. Note: This threshold may vary based on your Tally company data size, "
                          f"backup frequency, and additional features like auto-sync of stock items or ledger details.")
                CLogger.MCWriteLog("error", f"Insufficient disk space: {free_gb:.2f}GB available, 20GB required")
                # print(f"⚠️ Insufficient disk space for AccuVelocity operation!")
                return False, comment
            # Warning if disk usage is high but still has 20GB+ free
            elif used_percent > 80:
                comment = (f"Your disk usage on {path} is {used_percent:.2f}%, which is high. You have {free_gb:.2f}GB free space "
                          f"which is sufficient for AccuVelocity, but consider freeing up more space for system stability. "
                          f"The space requirements may increase depending on your Tally company data size, backup preferences, "
                          f"and enabled features such as auto-sync of stock items or ledger details.")
                CLogger.MCWriteLog("warning", f"High disk usage ({used_percent:.2f}%) but sufficient space for AccuVelocity ({free_gb:.2f}GB)")
                # print(f"⚖️ Disk space sufficient for AccuVelocity but system disk usage is high.")
                return True, comment

            CLogger.MCWriteLog("info", f"Sufficient disk space available: {free_gb:.2f}GB")
            print(f"✅ Sufficient disk space available: {free_gb:.2f}GB")
            return True, ""
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to analyze disk usage for {path}: {e}")
            print(f"Failed to analyze disk usage for {path}. ❌")
            comment = f"Failed to analyze disk usage for {path}: {e}"
            return False, comment

    def check_static_ip(self) -> tuple[bool, str]:
        """List all IPv4 addresses on interfaces (heuristic for 'static')."""
        CLogger.MCWriteLog("info", "Checking static IP addresses...")
        try:
            addrs = psutil.net_if_addrs()
            ipv4s = []
            for iface, alist in addrs.items():
                for addr in alist:
                    if addr.family == socket.AF_INET:
                        ipv4s.append((iface, addr.address))

            if ipv4s:
                CLogger.MCWriteLog("info", "Detected IPv4 addresses (interface: IP):")

                # Format the IP addresses for the comment
                ip_list = []
                for iface, ip in ipv4s:
                    CLogger.MCWriteLog("info", f" • {iface}: {ip}")
                    ip_list.append(f"{iface}: {ip}")

                # Create a user-friendly success message
                if len(ipv4s) == 1:
                    iface, ip = ipv4s[0]
                    comment = f"✓ Network interface '{iface}' is configured with IP address {ip}. This configuration is suitable for AccuVelocity operations."
                else:
                    # Find the primary interface (usually Ethernet or Wi-Fi)
                    primary_interfaces = [ip for iface, ip in ipv4s if "Ethernet" in iface or "Wi-Fi" in iface or "Wireless" in iface]
                    if primary_interfaces:
                        comment = f"✓ Multiple network interfaces detected. Primary IP: {primary_interfaces[0]}. Additional IPs: {', '.join(ip for ip in ip_list if ip not in primary_interfaces)}."
                    else:
                        comment = f"✓ Multiple network interfaces detected: {', '.join(ip_list)}. AccuVelocity will use the appropriate interface for communication."

                return True, comment
            else:
                CLogger.MCWriteLog("warning", "No IPv4 addresses detected.")
                comment = "Error: No IPv4 addresses detected. Please check your network configuration. AccuVelocity requires a valid network connection to function properly."
                return False, comment
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to check static IP addresses: {e}")
            comment = f"Error: Failed to check network configuration. Technical details: {str(e)}. Please ensure your network adapters are properly configured."
            return False, comment

    def check_and_update_dynamic_ip(self):
        CLogger.MCWriteLog("info", "Checking and updating dynamic IP...")
        try:
            public_ip = requests.get("https://api.ipify.org").text.strip()
            CLogger.MCWriteLog("info", f"Current external IP: {public_ip}")
        except requests.RequestException as e:
            CLogger.MCWriteLog("error", f"Failed to retrieve external IP: {e}")
            print("Failed to retrieve external IP. ❌")
            return False, f"Failed to retrieve external IP: {e}"

        try:
            # Get all available IPv4 addresses
            local_ips = []
            addrs = psutil.net_if_addrs()
            for iface, addr_list in addrs.items():
                for addr in addr_list:
                    if addr.family == socket.AF_INET:  # IPv4
                        # Skip loopback addresses
                        if not addr.address.startswith("127."):
                            local_ips.append((iface, addr.address))

            # Log all found IPs
            CLogger.MCWriteLog("info", f"Found local IPs: {local_ips}")

            # Use the first real IPv4 address (non-loopback)
            if local_ips:
                # Get the first physical network adapter's IP
                # This is typically the main Ethernet or WiFi connection
                for iface, ip in local_ips:
                    if "Ethernet" in iface or "Wi-Fi" in iface or "Wireless" in iface:
                        local_ip = ip
                        CLogger.MCWriteLog("info", f"Using IPv4 from {iface}: {ip}")
                        break
                else:
                    # If no preferred interface found, use the first one
                    local_ip = local_ips[0][1]
                    CLogger.MCWriteLog("info", f"Using first available IPv4: {local_ip}")
            else:
                # Fallback to the old method if no IPs found
                local_ip = socket.gethostbyname(socket.gethostname())
                CLogger.MCWriteLog("info", f"Falling back to hostname resolution: {local_ip}")

            CLogger.MCWriteLog("info", f"Selected IPv4 address: {local_ip}")

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to retrieve local IP: {e}")
            print("Failed to retrieve local IP. ❌")
            return False, f"Failed to retrieve local IP: {e}"

        req_url = self.user_config.get("ExportRequest", {}).get("ReqUrl", "")
        if not req_url:
            CLogger.MCWriteLog("error", "ReqUrl not found in user configuration.")
            print("ReqUrl not found in user configuration. ❌")
            return False, "ReqUrl not found in user configuration."
        try:
            parsed = urlparse(req_url)
            config_host = parsed.hostname
            config_port = parsed.port
            CLogger.MCWriteLog("info", f"Configured IP in ReqUrl: {config_host}")
            print(f"Current external IP: {public_ip}")
            print(f"Current local static IP: {local_ip}")
            print(f"Configured IP in ReqUrl: {config_host}")

            # Check if the configured host is a valid IP address
            try:
                socket.inet_aton(config_host)
                is_ip = True
            except socket.error:
                is_ip = False

            # If the configured host is not an IP address (e.g., localhost), don't update it
            if not is_ip and config_host.lower() in ['localhost', '127.0.0.1']:
                CLogger.MCWriteLog("info", f"Configured host is {config_host}, not updating.")
                print(f"Configured host is {config_host}, not updating. ✅")
                return True, ""

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to parse ReqUrl: {e}")
            print("Failed to parse ReqUrl. ❌")
            return False, f"Failed to parse ReqUrl: {e}"

        if local_ip != config_host:
            try:
                # Create new URL with updated IP but same port
                new_url = f"{parsed.scheme}://{local_ip}:{config_port}{parsed.path}"

                # Update the user_config in memory
                self.user_config["ExportRequest"]["ReqUrl"] = new_url

                # Get the path to UserConfig.json
                config_file = os.path.join(CGeneralHelper.MSGetResourceDirectory(), CGeneralHelper.MSGetUserConfigFileName())

                # Write the updated config back to file
                with open(config_file, 'w') as f:
                    json.dump(self.user_config, f, indent=4)

                comment = f"Updated ReqUrl in UserConfig.json from {req_url} to {new_url}"
                CLogger.MCWriteLog("info", comment)
                print(f"✅ {comment}")
                return True, comment
            except Exception as e:
                comment = f"Failed to update ReqUrl in UserConfig.json: {e}"
                CLogger.MCWriteLog("error", comment)
                print(f"❌ {comment}")
                return False, comment
        else:
            CLogger.MCWriteLog("info", "Local static IP matches the ReqUrl configuration.")
            print("Local static IP matches the ReqUrl configuration. ✅")
            return True, ""

    def check_accuvelocity_server(self) -> tuple[bool, str]:
        """Check if any API endpoint from AccuveloctyConfig.json is working."""
        CLogger.MCWriteLog("info", "Checking Accuvelocity server availability...")
        print("Checking Accuvelocity server availability...")

        # Get API endpoints directly from MSGetAccuvelocityConfig
        try:
            # Get AccuvelocityConfig directly using the helper method
            accu_config = CResoucedataHelper.MSGetAccuvelocityConfig()

            # This will always return a dictionary with at least empty apiEndpoints
            api_endpoints = accu_config.get("apiEndpoints", [])

            if not api_endpoints:
                CLogger.MCWriteLog("error", "No API endpoints found in AccuveloctyConfig.json.")
                print("No API endpoints found in AccuveloctyConfig.json. ❌")
                comment = "No API endpoints found in AccuveloctyConfig.json. Please check your configuration."
                return False, comment

            # Get the original URL from user config
            original_url = self.user_config.get("ExportRequest", {}).get("ReqUrl", "") or \
                        (f"{api_endpoints[0]}api/tally/Export" if api_endpoints else "")

            if not original_url:
                CLogger.MCWriteLog("error", "No valid original URL to test API endpoints.")
                print("No valid original URL to test API endpoints. ❌")
                comment = "No valid original URL to test API endpoints. Please check your configuration."
                return False, comment

            # Try to find a working endpoint
            try:
                working_endpoint = CArgumentParser.MSGet_working_endpoint(original_url, api_endpoints)
                CLogger.MCWriteLog("info", f"Accuvelocity server is correctly working: {working_endpoint} ✅")
                print("Accuvelocity server is correctly working. ✅")

                # Create a detailed success message
                server_name = urlparse(working_endpoint).netloc
                comment = f"✓ Successfully connected to AccuVelocity server at {server_name}. The server is operational and ready to process your requests. All AccuVelocity services are available."
                return True, comment

            except Exception as e:
                CLogger.MCWriteLog("error", f"All Accuvelocity servers are unavailable: {e}")
                print("All Accuvelocity servers are currently unavailable. ❌")

                # Create a detailed error message with troubleshooting steps
                comment = "Error: Unable to connect to AccuVelocity servers. We're experiencing temporary connectivity issues with our servers.\n\n" + \
                          "Troubleshooting steps:\n" + \
                          "1. Check your internet connection\n" + \
                          "2. Verify your firewall settings are not blocking AccuVelocity\n" + \
                          "3. Try again in a few minutes\n\n" + \
                          "Our technical team has been notified and is working to restore service as quickly as possible. For immediate assistance or updates, please visit Accuvelocity.com or contact our support <NAME_EMAIL>."
                return False, comment

        except Exception as e:
            CLogger.MCWriteLog("error", f"Unexpected error checking API endpoints: {e}")
            print(f"Unexpected error checking API endpoints: {e} ❌")
            comment = f"Unexpected error checking API endpoints: {e}"
            return False, comment

    @staticmethod
    def get_tallyprime_version(exe_path: str) -> str:
        """Reads the FileVersion from the PE header of the given executable."""
        try:
            pe = pefile.PE(exe_path)
            for fileinfo in getattr(pe, 'FileInfo', []) or []:
                for entry in fileinfo:
                    if entry.Key == b'StringFileInfo':
                        for st in entry.StringTable:
                            raw = st.entries.get(b'FileVersion')
                            if raw:
                                return raw.decode(errors='ignore')
            return "Version information not found."
        except Exception as e:
            return f"Error retrieving version: {e}"

    def find_tallyprime_installations(self, search_paths: list[str] = None, scan_entire_drives: bool = False) -> list[tuple[str, str]]:
        """Scans for tally.exe and logs its path and version."""
        if search_paths is None:
            if scan_entire_drives:
                try:
                    output = subprocess.check_output(["where", "/R", "C:\\", "tally.exe"], stderr=subprocess.DEVNULL, text=True)
                    search_paths = list({os.path.dirname(p) for p in output.splitlines()})
                except subprocess.CalledProcessError as e:
                    CLogger.MCWriteLog("error", f"Failed to scan drives for tally.exe: {e}")
                    self.accu_velocity_comments.append(f"Failed to scan drives for tally.exe: {e}")
                    search_paths = glob.glob(r"C:\Program Files\TallyPrime*")
            else:
                search_paths = glob.glob(r"C:\Program Files\TallyPrime*")

        found = []
        for base in search_paths:
            if not os.path.isdir(base):
                print(f"Warning: Search path does not exist: {base}")
                continue
            try:
                for root, _, files in os.walk(base):
                    if any(f.lower() == 'tally.exe' for f in files):
                        exe = os.path.join(root, 'tally.exe')
                        ver = self.get_tallyprime_version(exe)
                        print(f"Found: {exe} → {ver}")
                        found.append((exe, ver))
            except Exception as e:
                CLogger.MCWriteLog("error", f"Error scanning directory {base}: {e}")
                self.accu_velocity_comments.append(f"Error scanning directory {base}: {e}")
        if not found:
            self.accu_velocity_comments.append("No TallyPrime installations found. Please install TallyPrime.")
        return found

    def check_tallyprime_version(self, required_versions: list[str] = None, search_paths: list[str] = None, scan_entire_drives: bool = False) -> tuple[bool, str]:
        """Verifies all discovered TallyPrime installations match one of the required_versions."""
        if required_versions is None:
            required_versions = ["1.1.5.1", "1.1.4.1", "1.1.6.0"]
        results = self.find_tallyprime_installations(search_paths, scan_entire_drives)
        if not results:
            warn = "No TallyPrime installations found."
            CLogger.MCWriteLog("warning", warn)
            print(f"⚠️ {warn}")
            return False, warn
        all_ok = True
        comments = []
        for exe_path, ver in results:
            if ver not in required_versions:
                msg = f"Tally version {ver} at {exe_path} is not supported by Accuvelocity. Please update to one of: {', '.join(required_versions)}."
                CLogger.MCWriteLog("warning", msg)
                print(f"❌ {msg}")
                comments.append(msg)
                all_ok = False
            else:
                msg = f"Tally version {ver} at {exe_path} is supported."
                CLogger.MCWriteLog("info", msg)
                print(f"✅ {msg}")
        if not all_ok:
            comment = "\n".join(comments)
            return False, comment
        return True, ""

    def check_tallyprime_config(self, search_paths: list[str] = None, scan_entire_drives: bool = False, version: str = '1.1.5.1') -> tuple[bool, str]:
        """Reads tally.ini beside each tally.exe, updates Config.xml and UserConfig.json."""
        CLogger.MCWriteLog("info", "Checking TallyPrime configuration...")
        results = self.find_tallyprime_installations(search_paths, scan_entire_drives)
        if not results:
            warn = "No TallyPrime installations found for config checks."
            CLogger.MCWriteLog("warning", warn)
            print(f"⚠️ {warn}")
            return False, warn
        selected = next(((exe, ver) for exe, ver in results if ver == version), None) or results[0]
        exe_path, selected_version = selected
        base_dir = os.path.dirname(exe_path)
        ini_path = os.path.join(base_dir, 'tally.ini')
        config_xml = os.path.join(base_dir, 'Config.xml')
        user_config_path = CResoucedataHelper.MSGetUserConfigLocation()
        if not os.path.isfile(ini_path):
            warn = f"tally.ini not found in {base_dir}."
            CLogger.MCWriteLog("warning", warn)
            print(f"⚠️ {warn}")
            return False, f"{warn} Please ensure TallyPrime is properly installed."
        try:
            # Read the tally.ini file
            with open(ini_path, 'r') as f:
                ini_content = f.readlines()

            # Parse the ini file, handling multiple entries with the same key
            ini_data = {}
            tdl_count = 0

            for line in ini_content:
                line = line.strip()
                # Skip comments and section headers
                if not line or line.startswith(';') or line.startswith('['):
                    continue

                # Split by the first equals sign
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().rstrip(',')

                    # Handle multiple TDL entries
                    if key == 'TDL':
                        if 'TDL' in ini_data:
                            # If we already have a TDL entry, create numbered entries
                            tdl_count += 1
                            ini_data[f'TDL{tdl_count}'] = value
                        else:
                            ini_data[key] = value
                    else:
                        ini_data[key] = value

            # Log the parsed ini data
            CLogger.MCWriteLog("info", f"Parsed tally.ini data: {ini_data}")

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to read tally.ini: {e}")
            print(f"Failed to read tally.ini: {e} ❌")
            return False, f"Failed to read tally.ini: {e}"
        comments = []
        ok = True
        # Check User TDL and TDL paths
        user_tdl = ini_data.get('User TDL', '').lower()

        # Handle multiple TDL entries
        # First, collect all TDL entries from the ini_data dictionary
        tdl_paths = []
        for key, value in ini_data.items():
            if key == 'TDL':
                tdl_paths.append(value)
            # Also check for numbered TDL keys like TDL1, TDL2, etc.
            elif key.startswith('TDL') and key[3:].isdigit():
                tdl_paths.append(value)

        # Log all found TDL paths
        CLogger.MCWriteLog("info", f"Found TDL paths: {tdl_paths}")

        # Check if AccuVelocity-AI.tcp is in any of the TDL paths
        accuvelocity_tdl_loaded = False
        for tdl_path in tdl_paths:
            if 'AccuVelocity-AI.tcp' in tdl_path:
                accuvelocity_tdl_loaded = True
                CLogger.MCWriteLog("info", f"Found AccuVelocity-AI.tcp in path: {tdl_path}")
                break

        if user_tdl != 'yes' or not accuvelocity_tdl_loaded:
            warn = "AccuVelocity TCP file is not correctly loaded."
            CLogger.MCWriteLog("warning", warn)
            print(f"❌ {warn}")
            comments.append(f"{warn} TODO: To load the TCP file in TallyPrime, follow these steps:\n1. Open TallyPrime.\n2. Go to 'Help' > 'TDL & Add-Ons'.\n3. Select 'Load TDL' and choose 'AccuVelocity-AI.tcp' from the AccuVelocity Resources folder.")
            ok = False
        else:
            CLogger.MCWriteLog("info", "AccuVelocity TCP file is correctly loaded.")
            print("✅ AccuVelocity TCP file is correctly loaded.")
        # Update Config.xml
        data_path = ini_data.get('Data')
        if data_path:
            try:
                tree = ET.parse(config_xml)
                root = tree.getroot()
                for node in root.findall('.//TallyComDataPath'):
                    node.text = data_path
                tree.write(config_xml, encoding='utf-8', xml_declaration=True)
                CLogger.MCWriteLog("info", f"Updated Config.xml TallyComDataPath to {data_path}.")
                print(f"✅ Updated Config.xml TallyComDataPath to {data_path}.")
            except Exception as e:
                CLogger.MCWriteLog("error", f"Failed to update Config.xml: {e}")
                print(f"Failed to update Config.xml: {e} ❌")
                comments.append(f"Failed to update Config.xml: {e}")
                ok = False
        # Check Client Server setting
        if ini_data.get('Client Server') != 'Both':
            warn = "'Client Server' is not set to 'Both' in tally.ini."
            CLogger.MCWriteLog("warning", warn)
            print(f"❌ {warn} Please set it to Both.")
            comments.append(f"{warn} Please set it to 'Both'.")
            ok = False
        else:
            CLogger.MCWriteLog("info", "'Client Server' is set to Both.")
            print("✅ 'Client Server' is set to Both.")
        # Update UserConfig.json port
        port = ini_data.get('ServerPort')
        if port:
            try:
                with open(user_config_path, 'r+') as f:
                    cfg = json.load(f)
                    parsed = urlparse(cfg.get("ExportRequest", {}).get("ReqUrl", ""))
                    if str(parsed.port) != port:
                        new_netloc = f"{parsed.hostname}:{port}"
                        new_url = urlunparse((parsed.scheme, new_netloc, parsed.path, '', '', ''))
                        cfg["ExportRequest"]['ReqUrl'] = new_url
                        f.seek(0)
                        json.dump(cfg, f, indent=4)
                        f.truncate()
                        CLogger.MCWriteLog("info", f"Updated {user_config_path} ReqUrl to use port {port}.")
                        print(f"✅ Updated {user_config_path} ReqUrl to use port {port}.")
            except Exception as e:
                CLogger.MCWriteLog("error", f"Failed to update {user_config_path}: {e}")
                print(f"Failed to update {user_config_path}: {e} ❌")
                comments.append(f"Failed to update {user_config_path}: {e}")
                ok = False
        # Check ODBC Server
        if ini_data.get('Enable ODBC Server', '').lower() != 'yes':
            warn = "ODBC Server is disabled."
            CLogger.MCWriteLog("warning", warn)
            print(f"❌ {warn} Enable it via F12 > Advanced Configuration.")
            comments.append(f"{warn} Please enable it via F12 > Advanced Configuration > Set 'Enable ODBC Server' to Yes.")
            ok = False
        else:
            CLogger.MCWriteLog("info", "ODBC Server is enabled.")
            print("✅ ODBC Server is enabled.")
        if not ok:
            comment = "\n".join(comments)
            return False, comment
        return True, ""

    def check_tally_prime_server(self) -> tuple[bool, str]:
        """Check if the TallyPrime server is running based on the ReqUrl in UserConfig.json."""
        CLogger.MCWriteLog("info", "Checking TallyPrime server availability...")
        print("Checking TallyPrime server availability...")
        try:
            req_url = self.user_config.get("ExportRequest", {}).get("ReqUrl", "")
            if not req_url:
                CLogger.MCWriteLog("error", "ReqUrl not found in UserConfig.json.")
                print("ReqUrl not found in UserConfig.json. ❌")
                comment = "ReqUrl not found in UserConfig.json."
                return False, comment
            response = requests.get(req_url, timeout=10)
            if response.status_code == 200:
                try:
                    root = ET.fromstring(response.text)
                    if root.tag == "RESPONSE" and root.text.strip() == "TallyPrime Server is Running":
                        CLogger.MCWriteLog("info", "TallyPrime server is running successfully ✅")
                        print("TallyPrime server is running successfully. ✅")

                        # Create a detailed success message
                        server_url = urlparse(req_url).netloc
                        comment = f"✓ TallyPrime server is running and accessible at {server_url}. The server is properly configured and ready to process AccuVelocity requests. All TallyPrime integration features are available."
                        return True, comment
                    else:
                        CLogger.MCWriteLog("error", "TallyPrime server response is not as expected.")
                        print("TallyPrime server response is not as expected. ❌")

                        comment = "Error: TallyPrime server response is not in the expected format.\n\n" + \
                                  "Troubleshooting steps:\n" + \
                                  "1. Verify TallyPrime is running with the correct version\n" + \
                                  "2. Check if TallyPrime TCP files are properly loaded\n" + \
                                  "3. Restart TallyPrime and try again"
                        return False, comment
                except ET.ParseError:
                    CLogger.MCWriteLog("error", "Failed to parse TallyPrime server response as XML.")
                    print("Failed to parse TallyPrime server response as XML. ❌")

                    comment = "Error: Unable to interpret the response from TallyPrime server.\n\n" + \
                              "Troubleshooting steps:\n" + \
                              "1. Verify TallyPrime is running with the correct version\n" + \
                              "2. Check if TallyPrime TCP files are properly loaded\n" + \
                              "3. Restart TallyPrime and try again"
                    return False, comment
            else:
                CLogger.MCWriteLog("error", f"Failed to connect to TallyPrime server: HTTP {response.status_code}")
                print(f"Failed to connect to TallyPrime server: HTTP {response.status_code} ❌")

                comment = "Error: Failed to connect to TallyPrime server (HTTP Status: " + str(response.status_code) + ").\n\n" + \
                          "Troubleshooting steps:\n" + \
                          "1. Ensure TallyPrime is running\n" + \
                          "2. Verify the TallyPrime server port is correctly configured\n" + \
                          "3. Check if any firewall is blocking the connection\n" + \
                          "4. Verify the ReqUrl in UserConfig.json is correct"
                return False, comment

        except requests.exceptions.RequestException as e:
            CLogger.MCWriteLog("error", f"Error connecting to TallyPrime server: {e}")
            print(f"Error connecting to TallyPrime server: {e} ❌")

            # Provide a user-friendly error message based on the type of exception
            if isinstance(e, requests.ConnectionError):
                comment = "Error: Unable to establish a connection to the TallyPrime server.\n\n" + \
                          "Troubleshooting steps:\n" + \
                          "1. Ensure TallyPrime is running\n" + \
                          "2. Verify the TallyPrime server port is correctly configured\n" + \
                          "3. Check if any firewall is blocking the connection"
            elif isinstance(e, requests.Timeout):
                comment = "Error: The connection to TallyPrime server timed out.\n\n" + \
                          "Troubleshooting steps:\n" + \
                          "1. Check if TallyPrime is responding to requests\n" + \
                          "2. Verify your system resources are not overloaded\n" + \
                          "3. Restart TallyPrime and try again"
            else:
                comment = f"Error: Failed to connect to TallyPrime server. Technical details: {str(e)}.\n\n" + \
                          "Please ensure TallyPrime is running and properly configured."
            return False, comment
        except Exception as e:
            CLogger.MCWriteLog("error", f"Unexpected error checking TallyPrime server: {e}")
            print(f"Unexpected error checking TallyPrime server: {e} ❌")

            comment = "Error: An unexpected issue occurred while checking the TallyPrime server.\n\n" + \
                      "Troubleshooting steps:\n" + \
                      "1. Ensure TallyPrime is running\n" + \
                      "2. Verify your ReqUrl in UserConfig.json is correct\n" + \
                      "3. Restart TallyPrime and try again\n\n" + \
                      f"Technical details: {str(e)}"
            return False, comment

    def check_license_file(self, search_paths: list[str] = None, scan_entire_drives: bool = False, version: str = '1.1.5.1') -> tuple[bool, str]:
        """Checks if license.lic exists in the AccuVelocity folder of the selected TallyPrime installation."""
        CLogger.MCWriteLog("info", "Checking for license.lic...")
        try:
            results = self.find_tallyprime_installations(search_paths, scan_entire_drives)
            if not results:
                warn = "No TallyPrime installations found."
                CLogger.MCWriteLog("warning", warn)
                print(f"⚠️ {warn}")
                return False, warn
            selected = next(((exe, ver) for exe, ver in results if ver == version), None) or results[0]
            exe_path, selected_version = selected
            base_dir = os.path.dirname(exe_path)
            license_path = os.path.join(base_dir, "AccuVelocity", "license.lic")
            if os.path.isfile(license_path):
                CLogger.MCWriteLog("info", f"license.lic found at {license_path}")
                print(f"license.lic found at {license_path} ✅")
                return True, ""
            else:
                warn = f"license.lic not found at {license_path}"
                CLogger.MCWriteLog("warning", warn)
                print(f"⚠️ {warn}")
                return False, warn
        except Exception as e:
            error_message = f"An error occurred while checking for license.lic: {e}"
            CLogger.MCWriteLog("error", error_message)
            print(f"❌ {error_message}")
            return False, f"{error_message}\nPlease add license.lic file."

    def check_important_files_and_folders(self, search_paths: list[str] = None, scan_entire_drives: bool = False, version: str = '1.1.5.1') -> tuple[bool, str]:
        """Checks if important files and folders exist in the Resources directory of the selected TallyPrime installation."""
        CLogger.MCWriteLog("info", "Checking for important files and folders...")
        try:
            # Find TallyPrime installations
            results = self.find_tallyprime_installations(search_paths, scan_entire_drives)
            if not results:
                warn = "No TallyPrime installations found."
                CLogger.MCWriteLog("warning", warn)
                print(f"⚠️ {warn}")
                return False, warn

            # Select the installation matching the specified version, or default to the first one
            selected = next(((exe, ver) for exe, ver in results if ver == version), None) or results[0]
            exe_path, selected_version = selected

            # Determine the base directory and Resources path
            base_dir = os.path.dirname(exe_path)
            resources_path = os.path.join(base_dir,"AccuVelocity", "Resources")

            # List of files and folders to check
            required_files = ["UserConfig.json", "DeveloperConfig.json", "AccuveloctyConfig.json"]
            required_folders = ["Advertisement", "Requests"]

            # Check for each file
            missing_files = []
            for file in required_files:
                file_path = os.path.join(resources_path, file)
                if not os.path.isfile(file_path):
                    missing_files.append(file)

            base_dir = os.path.join(base_dir,"AccuVelocity")
            # Check for each folder
            missing_folders = []
            for folder in required_folders:
                folder_path = os.path.join(base_dir, folder)
                if not os.path.isdir(folder_path):
                    missing_folders.append(folder)

            # Construct the comment based on missing items
            if missing_files or missing_folders:
                comment_parts = []
                if missing_files:
                    comment_parts.append(f"Missing files: {', '.join(missing_files)}")
                if missing_folders:
                    comment_parts.append(f"Missing folders: {', '.join(missing_folders)}")
                comment = " and ".join(comment_parts) + "."
                CLogger.MCWriteLog("warning", comment)
                print(f"⚠️ {comment}")
                return False, comment
            else:
                CLogger.MCWriteLog("info", "All important files and folders are present ✅")
                print("All important files and folders are present ✅")
                return True, ""

        except Exception as e:
            error_message = f"An error occurred while checking for important files and folders: {e}"
            CLogger.MCWriteLog("error", error_message)
            print(f"❌ {error_message}")
            return False, error_message

    def MSRunDiagnostics(self, scan_entire_drives: bool = False) -> list[tuple[str, bool, str]]:
        """Runs a series of system diagnostics, including TallyPrime discovery."""
        CLogger.MCWriteLog("info", "Starting system diagnostics...")
        diagnostics = [
            ("Internet Connectivity", self.check_internet_connectivity()),
            ("Internet Speed", self.internet_speed_test()),
            ("RAM Usage", self.ram_usage_analysis()),
            ("CPU Usage", self.cpu_usage_analysis()),
            ("Disk Usage", self.disk_usage_analysis()),
            ("Static IP", self.check_static_ip()),
            ("Dynamic IP", self.check_and_update_dynamic_ip()),
            ("Accuvelocity Server", self.check_accuvelocity_server()),
            ("TallyPrime Version", self.check_tallyprime_version(scan_entire_drives=scan_entire_drives)),
            ("TallyPrime Config", self.check_tallyprime_config(scan_entire_drives=scan_entire_drives)),
            ("TallyPrime Server", self.check_tally_prime_server()),
            ("License File", self.check_license_file(scan_entire_drives=scan_entire_drives)),
            ("Important Files & Folders", self.check_important_files_and_folders(scan_entire_drives=scan_entire_drives)),
        ]
        issues = []
        for name, (success, comment) in diagnostics:
            if not success:
                issues.append((name, comment))
        if issues:
            CLogger.MCWriteLog("warning", "Diagnostic issues found:")
            for name, comment in issues:
                CLogger.MCWriteLog("warning", f"{name}: {comment}")
                print(f"⚠️ {name}: {comment}")
        else:
            CLogger.MCWriteLog("info", "All diagnostics passed successfully.")
            print("All diagnostics passed successfully. ✅")
        CLogger.MCWriteLog("info", "Diagnostics complete.")
        print("Diagnostics complete.")
        return [(name, success, comment) for name, (success, comment) in diagnostics]

class CBackupFileManager:
    # PURPOSE: Manage Creation , Deletion, Alteration Base on User Config Backup Limits Configured.
    @staticmethod
    def MSGetBackupDirectory():
        dictUserConfig = None
        dictUserConfigFile = CResoucedataHelper.MSGetUserConfigLocation()
        with open(dictUserConfigFile, 'r') as f:
            dictUserConfig = json.load(f)
        config_xml_path = dictUserConfig.get("CONFIG_XML", "")
        try:
            tree = ET.parse(config_xml_path)
            root = tree.getroot()
            backup_dir = root.find(".//BackUpDest")
            return backup_dir.text if backup_dir is not None else ""
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to read backup directory: {e}")
            return ""
    
    @staticmethod
    def MSTrackBackupRecord():
        backup_dir = CBackupFileManager.MSGetBackupDirectory()
        tracking = {}

        try:
            for date_folder in os.listdir(backup_dir):
                date_path = os.path.join(backup_dir, date_folder)
                if os.path.isdir(date_path):
                    time_entries = []
                    for time_folder in os.listdir(date_path):
                        if os.path.isdir(os.path.join(date_path, time_folder)):
                            time_entries.append(time_folder)
                    tracking[date_folder] = sorted(time_entries)

            # Save to JSON
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            json_path = os.path.join(strResourceDirpath, "BackupFileTracking.json")
            with open(json_path, "w") as f:
                json.dump(tracking, f, indent=4)

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to track backups: {e}")
    
    @staticmethod
    def MSModifyBackupExceedFlag():

        user_config = CResoucedataHelper.MSGetUserConfig()
        today = datetime.now().strftime("%d-%b-%y")
        limit = user_config.get("BackupConfig", {}).get("DailyBackup", 5)
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        tracking_file = os.path.join(strResourceDirpath, "BackupFileTracking.json")

        try:
            if not os.path.exists(tracking_file):
                return
            with open(tracking_file, "r") as f:
                data = json.load(f)
            count_today = len(data.get(today, []))

            xml_path = user_config.get("CONFIG_XML", "")
            tree = ET.parse(xml_path)
            root = tree.getroot()
            flag_node = root.find(".//IsDayBackupLimitExceed")
            if flag_node is not None:
                flag_node.text = "Yes" if count_today > limit else "No"
                tree.write(xml_path)

        except Exception as e:
            from CustomLogger import CLogger
            CLogger.MCWriteLog("error", f"Failed to modify XML flag: {e}")

    @staticmethod
    def MSCleanBackupFiles():
        try:
            backup_dir = CBackupFileManager.MSGetBackupDirectory()
            user_config = CResoucedataHelper.MSGetUserConfig()

            daily_limit = user_config.get("BackupConfig", {}).get("DailyBackup", 5)
            weekly_limit = user_config.get("BackupConfig", {}).get("WeeklyBackup", 10)
            monthly_limit = user_config.get("BackupConfig", {}).get("MonthlyBackup", 21)

            today = datetime.now().date()
            all_backups = []  # list of tuples: (date_obj, time_str)
            daily_map = defaultdict(list)

            # Build maps of date -> time folders
            for date_folder in os.listdir(backup_dir):
                date_path = os.path.join(backup_dir, date_folder)
                if not os.path.isdir(date_path):
                    continue
                try:
                    date_obj = datetime.strptime(date_folder, "%d-%b-%y").date()
                except ValueError:
                    continue
                for time_folder in os.listdir(date_path):
                    if time_folder.isdigit():
                        all_backups.append((date_obj, time_folder))
                        daily_map[date_obj].append(time_folder)

            # Daily Cleanup: for today's backups only
            today_times = daily_map.get(today, [])
            if len(today_times) > daily_limit:
                # keep latest by numeric string descending
                to_keep = sorted(today_times, reverse=True)[:daily_limit]
                to_delete = set(today_times) - set(to_keep)
                for time_str in to_delete:
                    path = os.path.join(backup_dir, today.strftime("%d-%b-%y"), time_str)
                    shutil.rmtree(path, ignore_errors=True)
                    CLogger.MCWriteLog("info", f"Deleted excess daily backup: {path}")

            # Weekly Cleanup: one per day, up to weekly_limit
            week_groups = defaultdict(lambda: defaultdict(list))
            for date_obj, time_str in all_backups:
                iso_year, iso_week, _ = date_obj.isocalendar()
                week_key = f"{iso_year}-W{iso_week}"
                week_groups[week_key][date_obj].append(time_str)

            weekly_keep = set()
            for day_map in week_groups.values():
                weekly_keep |= CBackupFileManager._get_weekly_to_keep(day_map, weekly_limit)

            # Delete weekly excess (excluding daily deletions already done)
            for date_obj, time_str in all_backups:
                if (date_obj, time_str) not in weekly_keep and date_obj != today:
                    path = os.path.join(backup_dir, date_obj.strftime("%d-%b-%y"), time_str)
                    shutil.rmtree(path, ignore_errors=True)
                    CLogger.MCWriteLog("info", f"Deleted weekly backup: {path}")

            # Monthly Cleanup: distribute monthly_limit across weeks
            month_groups = defaultdict(list)
            for date_obj, time_str in all_backups:
                month_key = date_obj.strftime("%Y-%m")
                month_groups[month_key].append((date_obj, time_str))

            monthly_keep = set()
            for backups in month_groups.values():
                monthly_keep |= CBackupFileManager._get_monthly_to_keep(backups, monthly_limit)

            # Delete monthly excess
            for date_obj, time_str in all_backups:
                if (date_obj, time_str) not in monthly_keep and date_obj != today:
                    path = os.path.join(backup_dir, date_obj.strftime("%d-%b-%y"), time_str)
                    shutil.rmtree(path, ignore_errors=True)
                    CLogger.MCWriteLog("info", f"Deleted monthly backup: {path}")

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to clean backups: {e}")

    @staticmethod
    def _get_weekly_to_keep(day_map, limit):
        """
        day_map: Dict[date_obj -> list of time_str]
        Return set of (date_obj, time_str) to keep for weekly retention.
        Preference: one per day (latest), iterating days from Sunday backward,
        fill extra slots on latest days if days < limit.
        """
        # Sort days by weekday: Sunday(6) to Monday(0)
        days = sorted(day_map.keys(), key=lambda d: d.weekday(), reverse=True)
        # weekday(): Monday=0 ... Sunday=6; reverse sorts Sunday first
        keep = set()
        # First pass: one per distinct day
        for date_obj in days:
            if len(keep) >= limit:
                break
            times = sorted(day_map[date_obj], reverse=True)
            keep.add((date_obj, times[0]))

        # If still slots left, allocate extra on latest days
        if len(keep) < limit:
            needed = limit - len(keep)
            # flatten remaining times sorted by date desc then time desc
            extras = []
            for date_obj in days:
                times = sorted(day_map[date_obj], reverse=True)
                # skip first (already kept)
                for t in times[1:]:
                    extras.append((date_obj, t))
            extras.sort(key=lambda x: (x[0], x[1]), reverse=True)
            for date_obj, t in extras[:needed]:
                keep.add((date_obj, t))
        return keep

    @staticmethod
    def _get_monthly_to_keep(backups, monthly_limit):
        """
        backups: List of (date_obj, time_str) for a single month
        Distribute monthly_limit across ISO weeks within that month,
        then apply weekly_to_keep per week.
        """
        # Group by ISO week number
        week_map = defaultdict(lambda: defaultdict(list))
        for date_obj, time_str in backups:
            _, iso_week, _ = date_obj.isocalendar()
            week_map[iso_week][date_obj].append(time_str)

        weeks = sorted(week_map.keys())
        num_weeks = len(weeks)
        if num_weeks == 0:
            return set()
        base = monthly_limit // num_weeks
        remainder = monthly_limit % num_weeks
        # Distribute remainder to latest weeks
        latest_weeks = sorted(weeks, reverse=True)[:remainder]

        keep = set()
        for week in weeks:
            limit = base + (1 if week in latest_weeks else 0)
            if limit > 0:
                keep |= CBackupFileManager._get_weekly_to_keep(week_map[week], limit)
        return keep

  

    @staticmethod
    def MSCreateBackUpFileReport():

        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        CBackupFileManager.MSTrackBackupRecord()
        tracking_file = os.path.join(strResourceDirpath, "BackupFileTracking.json")
        report_file = os.path.join(strResourceDirpath, "BackupFileReport.json")
        result = {
            "DayWise": {},
            "WeekWise": defaultdict(list),
            "MonthWise": defaultdict(list)
        }

        try:
            with open(tracking_file, "r") as f:
                data = json.load(f)

            for date_str, times in data.items():
                dt = datetime.strptime(date_str, "%d-%b-%y")
                week = f"{dt.isocalendar().year}-W{dt.isocalendar().week}"
                month = dt.strftime("%Y-%m")

                result["DayWise"][date_str] = {
                    "count": len(times),
                    "timestamps": sorted(times)
                }
                result["WeekWise"][week].extend(times)
                result["MonthWise"][month].extend(times)

            with open(report_file, "w") as f:
                json.dump(result, f, indent=4)

        except Exception as e:
            from CustomLogger import CLogger
            CLogger.MCWriteLog("error", f"Failed to create backup report: {e}")

    @staticmethod
    def MSShowBackupReportGUI():
        """Launch the Backup Report GUI"""
        try:
            from BackupReportUI import CBackupReportWindow

            # Create backup report first
            CBackupFileManager.MSCreateBackUpFileReport()

            # Check if QApplication already exists
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)

            # Create and show the backup report window
            window = CBackupReportWindow()
            window.show()

            # Only exec if this is the main application
            if app is not None and hasattr(app, 'exec_'):
                return app.exec_()

        except Exception as e:
            from CustomLogger import CLogger
            CLogger.MCWriteLog("error", f"Failed to show backup report GUI: {e}")

    @staticmethod
    def startBackupManager():
        try:
            CBackupFileManager.MSTrackBackupRecord()
            CBackupFileManager.MSModifyBackupExceedFlag()
            CBackupFileManager.MSCleanBackupFiles()
        except Exception as e:
            from CustomLogger import CLogger
            CLogger.MCWriteLog("error", f"Failed to start backup manager: {e}")
        
def process_xml_files(input_dir, output_dir, url, strToken):
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    lsReceivedXMLFiles = []
    # Iterate over files in the given directory
    for filename in os.listdir(input_dir):
        if filename.lower().endswith(".xml"):  # Check if it's an XML file
            input_path = os.path.join(input_dir, filename)
            output_filename = f"TallyXMLResponse-{filename}"
            output_path = os.path.join(output_dir, output_filename)
            lsReceivedXMLFiles.append(output_path)
            # Create the file first
            with open(output_path, 'w') as f:
                pass
            # Call the method
            strExportedAbstractDataComment = CTallyExportHelper.MSExportTallyData(
                strRequestXMLPath=input_path,
                strResponseXMLPath=output_path,
                url=url
            )
            time.sleep(2)
            print(f"Processed: {input_path} -> {output_path}")
    else:
        if lsReceivedXMLFiles:
            # AccuVelocityConfig and DeveloperConfig Get
            dictInstalledExeDetail = CResoucedataHelper.MSGetAccuvelocityConfig()
            dictDeveloperConfig = CResoucedataHelper.MSGetDeveloperConfig()
            dictAPIEndPoints = dictDeveloperConfig.get("ExportRequest", {}).get("APIEndPoints", {})
            # API call to AHM server
            strClientRespUrl = dictAPIEndPoints.get("ClientResp", "")
            try:
                strClientRespUrl=str(CArgumentParser.MSGet_working_endpoint(strClientRespUrl,dictInstalledExeDetail.get("apiEndpoints",[])))
                CLogger.MCWriteLog("info",f"Working URL selected: {strClientRespUrl}")
            except Exception as e:
                CLogger.MCWriteLog("error",f"Error: {str(e)}")

            dictInstalledExeDetail["ClientImportedXMLType"] = "Manual_Import"
            dictInstalledExeDetail["ClientReqGeneratedAt"] = ""
            dictInstalledExeDetail["ClientReqImportedAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()

            # Send Manual Imported XML Response to AHM Server's
            dictAPIResponse = CTallyExportHelper.MSSendTallyImportedXMLResToServer(dictInstalledExeDetail = dictInstalledExeDetail, strTallyResponseXMLDir=output_dir, lsXMLFiles=lsReceivedXMLFiles, strURL=strClientRespUrl, strToken=strToken)
            CLogger.MCWriteLog("info",f"AHM Server API Response : {dictAPIResponse}")

        else:
            CLogger.MCWriteLog("info",f"No Tally Response XML File Found : {lsReceivedXMLFiles}")

def update_daybook_database(strToken:str, strReportFilePath:str):
    
    try:
        headers = {
                "Authorization": f"Bearer {strToken}"
            }
        
        data = {}

        doc = CDocument(strReportFilePath)
        file_content = doc.read_file()
        dictLicenseData = CLicenseHelper.MSVerifyLicense()
        dictDeveloperConfig = CResoucedataHelper.MSGetDeveloperConfig()
        iUserId = dictLicenseData['uid']
        bDevMode = dictLicenseData['DEVELOPER_MODE']
        strDayBookReqUrl = dictDeveloperConfig['ExportRequest']['APIEndPoints']['ExportDayBookUrl']
        file_content = zlib.compress(file_content)
        file_content = base64.b64encode(file_content).decode('utf-8')
        with httpx.Client(timeout=600) as client:
                response = client.post(
                  strDayBookReqUrl,
                    headers=headers,
                    json={"binary_file_content": file_content, "iUserId" : iUserId, "bDevMode" : bDevMode}
                )

        if response.status_code == 200:
            data = response.json()
            CLogger.MCWriteLog("info", f"Client request delivery API call completed successfully with response: {response.json()}.")
            return data
        else:
            CLogger.MCWriteLog("error", f"Client request delivery API call failed with status code {response.status_code} and response: {response.text}.")
            return None

    except Exception as e:
        CLogger.MCWriteLog("error", f"Failed to update client request delivery status: {str(e)}")
        CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
        return None

if __name__ == "__main__":
    print(CGeneralHelper.MSGetUserConfigFileName())
    # import argparse

    # parser = argparse.ArgumentParser()
    # parser.add_argument('--get-backup-report', action='store_true')
    # args = parser.parse_args()

    # if args.get_backup_report:
    #     CBackupFileManager.MSCreateBackUpFileReport()
    # else:
    #     CBackupFileManager.startBackupManager()
    # input_directory = r"D:\Nisarg\AccuVelocity\GitIgnore\Shri Ram Enterprise"
    # output_directory = r"gitignore\Shri Ram Enterprise"
    # service_url = "http://************:10050/"
    # process_xml_files(input_directory, output_directory, service_url, "your_token_here")
    # df = pd.read_excel(r"H:\Customers\26_Gwalia\Problems to Solve\Multiple Vouchers XML Import Quickly\TallyPredicted_demo.xlsx")
    # CSplitXMLFile.MSplitAndSaveTallyXML(
    #     df=df,
    #     strCustomerName="Gwalia Sweets Pvt Ltd - (24-25)",
    #     exportFolderPath=r"Gitignore\GwaliaBankStatement - 3 Voucher Per XML",
    #     rows_per_file=3 # or any other number you want
    # )
    # process_xml_files(input_directory, output_directory, service_url)
    # CLogger.MCSetupLogging()

    # objDocument = CDocument(r"H:\AI Data\17_ParagTraders\15_AQUANT\TestLedgerFinder\T-AN-6672_On_ClientTally.pdf")
    # print(objDocument.checksum)

    # try:
    #     doc = CDocument(r"H:\AI Data\17_ParagTraders\15_AQUANT\TestLedgerFinder\T-AN-6672_On_ClientTally - Copy.pdf")
    #     print(doc.checksum)

    #     print(f"Filename: {doc.filename}")
    #     print(f"Content Type: {doc.content_type}")
    #     print(f"File Size: {doc.size} bytes")
    #     print(f"Checksum: {doc.checksum}")
    # except Exception as e:
    #     print(f"Error: {e}")


    # CLicenseHelper.MSVerifyLicense()
    # print(SECRET_KEY)


    # Example usage
    # xml_file_path = "SingleExportV1.xml"  # Replace with your actual file path
    # response = CTallyExportHelper.send_xml_to_tally(xml_file_path,response_file = "SingleExportV2Res.xml")
    # print(response)
    # print(CDocument.MSCalculateChecksum(r"C:\Users\<USER>\Desktop\customer\REAL\Accuvelocity_exe\Resources\Exports\Response\01_01_2025\exported_files.zip"))
    # strExportedAbstractDataComment = CTallyExportHelper.MSExportTallyData(
    #                     strRequestXMLPath=r"H:\AI Data\DailyData\Vedansh\2025_03_20\TallyXML_FEE RECIEPT 12000-12837-2.xml",
    #                     strResponseXMLPath=r"gitignore\213_test.xml",
    #                     url="http://************:11001/"
    #                 )
    # print("strExportedAbstractDataComment -------", strExportedAbstractDataComment)

    # obj=CRunDiagnostics()
    # ls=obj.MSRunDiagnostics()
    # print(ls)

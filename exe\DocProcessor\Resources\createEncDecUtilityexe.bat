@echo off

:: Update the paths based on the actual project structure
SET "VENV_PATH=D:\Customer\Real\Accuvelocity\Code\Accuvelocity_exe\envAccuvelocity"
SET "SCRIPT_RELATIVE_PATH=..\Sourcecode"
SET "OUTPUT_RELATIVE_PATH=..\exe\EncryptDecryptUtility"
SET "SCRIPT_NAME=EncryptResourceConfig.py"
SET "EXE_NAME=EncryptDecryptTool.exe"

:: Activate the virtual environment
IF EXIST "%VENV_PATH%\Scripts\activate.bat" (
    echo Activating virtual environment...
    CALL "%VENV_PATH%\Scripts\activate.bat"
) ELSE (
    echo ERROR: Virtual environment not found at "%VENV_PATH%"!
    pause
    exit /b 1
)

:: Navigate to the correct directory
cd /d "%~dp0%SCRIPT_RELATIVE_PATH%" || (
    echo ERROR: The directory "%~dp0%SCRIPT_RELATIVE_PATH%" does not exist!
    pause
    exit /b 1
)

:: Check if the script exists before running PyInstaller
IF NOT EXIST "%SCRIPT_NAME%" (
    echo ERROR: Script file "%SCRIPT_NAME%" not found in "%CD%"!
    pause
    exit /b 1
)

:: Create the output directory if it doesn't exist
IF NOT EXIST "%OUTPUT_RELATIVE_PATH%" (
    mkdir "%OUTPUT_RELATIVE_PATH%"
)

:: Run PyInstaller using the virtual environment's Python
echo Running PyInstaller...
pyinstaller --onefile --distpath "%OUTPUT_RELATIVE_PATH%" --name "%EXE_NAME%" "%SCRIPT_NAME%"

pause

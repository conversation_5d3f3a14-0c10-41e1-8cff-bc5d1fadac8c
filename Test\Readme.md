# AccuVelocity Testing Framework

The AccuVelocity testing framework is designed to facilitate automated testing of the AccuVelocity application. It includes a set of test cases and utilities to ensure the application's functionality and performance.

## Prerequisites

Before running the tests, make sure you have the following prerequisites in place:

1. The `testing.env` file created in the root directory of the project with the required environment variables.

# testing.env

ACCU_TEST_MODE=1

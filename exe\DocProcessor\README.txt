=====================================================================
AccuVelocity Document Processor - Console Visibility Guide
=====================================================================

The AccuVelocity Document Processor (AVDocProcessor.exe) completely eliminates
the console window when running in GUI mode, providing a clean user experience:

CONSOLE VISIBILITY BEHAVIOR:
---------------------------

1. GUI Mode (Default Application Usage):
   - When running the application normally or with the --gui-mode flag, the console window is completely hidden
   - The application relaunches itself as a detached process without a console window
   - This provides a clean user experience for regular GUI usage
   - The progress bar GUI will be shown without any black console window in the background

2. Command-Line Operations:
   - The console window is shown for operations that output information to the console
   - This includes:
     * --help: Displays command-line help
     * --version: Shows application version
     * --xml_files: Processing XML files
     * --export-all, --export-abstract, --export-detailed: Data export operations
     * Any other operation that needs to display information in the console

HOW TO USE:
----------

1. For regular GUI usage (no console window):
   AVDocProcessor.exe
   or explicitly:
   AVDocProcessor.exe --gui-mode

2. For command-line operations (console window visible):
   AVDocProcessor.exe --help
   AVDocProcessor.exe --version
   AVDocProcessor.exe --xml_files "path\to\xml\file.xml"
   AVDocProcessor.exe --export-all

TECHNICAL NOTES:
--------------
- The application uses a sophisticated approach to completely hide the console window in GUI mode
- When running in GUI mode, the application completely detaches from the console using Windows API
- This is done by calling the FreeConsole() function from the Windows kernel32.dll
- The console window is completely eliminated, not just hidden or minimized
- The GUI components are only initialized after detaching from the console
- This ensures a clean user experience with no visible console window
- In CLI mode, the console window remains visible and GUI components are minimally initialized

=====================================================================

﻿"""
TestHind.py - Hind vendor test for ICD

This file contains the test execution logic and configuration.
Helper functions are in HelperModule.py.

Directory Structure Created:
TestResults/[Input|Reference]/TestHind/test_ICDHind/filename_hash/ -> files + Logs

Note: ICD tests use Journal voucher type and zip files
"""

import os
import sys
import json
from datetime import datetime

# Add the Test directory to the path to import HelperModule
test_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if test_dir not in sys.path:
    sys.path.insert(0, test_dir)
from HelperModule import get_file_path, get_file_hash, execute_accuvelocity, compare_csv_files, compare_xml_files, log_message

# ================================
# GLOBAL CONFIGURATION
# ================================

CUSTOMER_NAME = "ICD"
TEST_FILE_NAME = "TestHind"
TEST_METHOD = "test_ICDHind"
RELEARN_MODE = True
INPUT_FILE = r"Test\Customer\ICD\FilesForTesting\Hind.zip"
LICENSE_FILE = r"Test\Customer\ICD\Licenses\ICD_5_license_DevMode_True.lic"
VOUCHER_TYPE = "Journal"
XML_IGNORE_TAGS = ["VOUCHERNUMBER", "UDF:_UDF_805347380.LIST", "LASTVCHID", "VCHNUMBER", "LASTCREATEDVCHID"]
CSV_IGNORE_COLUMNS = ["Received Date"]
FILE_EXTENSIONS_TO_COMPARE = [".csv", ".xml"]

# ================================
# END OF GLOBAL CONFIGURATION
# ================================


def test_ICDHind():
    """Main test function for Hind vendor"""
    print(" Starting Hind Test for ICD")
    print(f" Test: {TEST_FILE_NAME}.{TEST_METHOD}")
    print(f" Learning mode: {RELEARN_MODE}")
    print("=" * 50)

    # Setup base directory as current working directory
    base_dir = os.getcwd()

    # Resolve file paths
    input_file_path = get_file_path(base_dir, INPUT_FILE)
    license_file_path = get_file_path(base_dir, LICENSE_FILE)

    # Validate input files exist
    if not os.path.exists(input_file_path):
        print(f" Input file not found: {input_file_path}")
        return

    if not os.path.exists(license_file_path):
        print(f" License file not found: {license_file_path}")
        return

    # Calculate file hash for unique identification
    file_hash = get_file_hash(input_file_path)
    input_filename = os.path.splitext(os.path.basename(input_file_path))[0]
    unique_folder_name = f"{input_filename}_{file_hash}"

    # Create test directory structure
    mode_folder = "Input" if RELEARN_MODE else "Reference"
    test_dir = os.path.join(
        "Test", "Customer", CUSTOMER_NAME, "TestResults", mode_folder, 
        TEST_FILE_NAME, TEST_METHOD, unique_folder_name
    )
    
    # Create directories
    os.makedirs(test_dir, exist_ok=True)

    logs_dir = os.path.join(test_dir, "Logs")
    os.makedirs(logs_dir, exist_ok=True)
    log_file = os.path.join(logs_dir, f"test_execution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    log_message("info", f"Starting test: {TEST_FILE_NAME}.{TEST_METHOD}", log_file)
    log_message("info", f"Customer: {CUSTOMER_NAME}", log_file)

    try:
        # Step 1: Execute AccuVelocity
        print(" Step 1: Executing AccuVelocity...")
        success, execution_time = execute_accuvelocity(
            base_dir, input_file_path, license_file_path, VOUCHER_TYPE, test_dir, logs_dir, log_file
        )

        comparison_results = []

        if not success:
            print(" AccuVelocity execution failed")
            return

        # Step 2: File comparison (only in validation mode)
        if not RELEARN_MODE:
            print(" Step 2: Comparing files with reference...")
            
            # Find reference directory
            reference_dir = os.path.join(
                "Test", "Customer", CUSTOMER_NAME, "TestResults", "Input",
                TEST_FILE_NAME, TEST_METHOD, unique_folder_name
            )
            
            if not os.path.exists(reference_dir):
                print(f" Reference directory not found: {reference_dir}")
                print("   Run in learning mode first.")
                return

            # Compare files
            for root, dirs, files in os.walk(test_dir):
                for file in files:
                    if any(file.endswith(ext) for ext in FILE_EXTENSIONS_TO_COMPARE):
                        current_file = os.path.join(root, file)
                        relative_path = os.path.relpath(current_file, test_dir)
                        reference_file = os.path.join(reference_dir, relative_path)
                        
                        if os.path.exists(reference_file):
                            if file.endswith('.xml'):
                                result = compare_xml_files(current_file, reference_file, XML_IGNORE_TAGS)
                            elif file.endswith('.csv'):
                                result = compare_csv_files(current_file, reference_file, CSV_IGNORE_COLUMNS)
                            
                            result["relative_path"] = relative_path
                            result["file_type"] = "XML Request" if "request" in file.lower() else "CSV Report" if file.endswith('.csv') else "XML Response"
                            comparison_results.append(result)
                            
                            status = " PASS" if result.get("identical", False) else " FAIL"
                            print(f"  {status}: {relative_path}")

        # Generate test report
        test_report = {
            "test_info": {
                "success": success,
                "execution_time": execution_time,
                "test_method": TEST_METHOD,
                "customer": CUSTOMER_NAME,
                "relearn_mode": RELEARN_MODE,
                "test_directory": test_dir,
                "logs_directory": logs_dir
            },
            "timestamp": datetime.now().isoformat(),
            "directories": {
                "test_dir": test_dir,
                "reference_dir": reference_dir if not RELEARN_MODE else "",
                "logs_dir": logs_dir
            },
            "comparison_results": comparison_results,
            "summary": {
                "total_files": len(comparison_results),
                "passed": sum(1 for r in comparison_results if r.get("identical", False)),
                "failed": sum(1 for r in comparison_results if not r.get("identical", False))
            }
        }

        # Save JSON report
        json_report_path = os.path.join(logs_dir, f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(json_report_path, 'w', encoding='utf-8') as f:
            json.dump(test_report, f, indent=2, ensure_ascii=False)

        print(f"\n Test Results:")
        print(f"  Success: {success}")
        print(f"  Execution time: {execution_time:.2f} seconds")
        print(f"  Test directory: {test_dir}")
        print(f"  Logs directory: {logs_dir}")
        
        if comparison_results:
            print(f"  Files compared: {len(comparison_results)}")
            print(f"  Passed: {test_report['summary']['passed']}")
            print(f"  Failed: {test_report['summary']['failed']}")

        log_message("info", f"Test completed successfully", log_file)

    except Exception as e:
        error_msg = f"Test execution failed: {str(e)}"
        log_message("error", error_msg, log_file)
        print(f" {error_msg}")


if __name__ == "__main__":
    test_ICDHind()

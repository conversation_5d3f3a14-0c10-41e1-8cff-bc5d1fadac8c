<?xml version="1.0" ?>
<ENVELOPE>
    <HEADER>
        <TALLYREQUEST>Import Data</TALLYREQUEST>
    </HEADER>
    <BODY>
        <IMPORTDATA>
            <REQUESTDESC>
                <REPORTNAME>Vouchers</REPORTNAME>
                <STATICVARIABLES>
                    <SVCURRENTCOMPANY>Abhinav Infrabuild Pvt.Ltd.(24-26)</SVCURRENTCOMPANY>
                </STATICVARIABLES>
            </REQUESTDESC>
            <REQUESTDATA>
                <TALLYMESSAGE xmlns:UDF="TallyUDF">
                    <VOUCHER VCHTYPE="AV-MachineryGRN" ACTION="Create" OBJVIEW="Invoice Voucher View">
                        <ADDRESS.LIST TYPE="String">
                            <ADDRESS>Maruti Nagar Betma Road, Betma</ADDRESS>
                            <ADDRESS>Indore</ADDRESS>
                        </ADDRESS.LIST>
                        <BASICBUYERADDRESS.LIST TYPE="String"/>
                        <DATE>********</DATE>
                        <REFERENCEDATE>********</REFERENCEDATE>
                        <VCHSTATUSDATE>********</VCHSTATUSDATE>
                        <EFFECTIVEDATE>********</EFFECTIVEDATE>
                        <STATENAME>Madhya Pradesh</STATENAME>
                        <COUNTRYOFRESIDENCE>India</COUNTRYOFRESIDENCE>
                        <PARTYGSTIN>23BKHPK0698C1ZC</PARTYGSTIN>
                        <PLACEOFSUPPLY>Madhya Pradesh</PLACEOFSUPPLY>
                        <PARTYNAME>Ekta Suppliers</PARTYNAME>
                        <GSTREGISTRATION TAXTYPE="GST" TAXREGISTRATION="23AAHCA9425D1ZY"> Registration</GSTREGISTRATION>
                        <CMPGSTIN>23AAHCA9425D1ZY</CMPGSTIN>
                        <NARRATION>Machinery GRN for Machine No: MP 09 D.N 4447 (JCB) | -</NARRATION>
                        <VOUCHERTYPENAME>AV-MachineryGRN</VOUCHERTYPENAME>
                        <PARTYLEDGERNAME>Ekta Suppliers</PARTYLEDGERNAME>
                        <VOUCHERNUMBER/>
                        <REFERENCE>MP 09 D.N 4447 (JCB)</REFERENCE>
                        <BASICBUYERNAME>Abhinav Infrabuild Pvt.Ltd.</BASICBUYERNAME>
                        <CMPGSTREGISTRATIONTYPE>Regular</CMPGSTREGISTRATIONTYPE>
                        <PARTYMAILINGNAME>Ekta Suppliers</PARTYMAILINGNAME>
                        <CONSIGNEEGSTIN>23AAHCA9425D1ZY</CONSIGNEEGSTIN>
                        <CONSIGNEEMAILINGNAME>Abhinav Infrabuild Pvt.Ltd.</CONSIGNEEMAILINGNAME>
                        <CONSIGNEEPINCODE/>
                        <CONSIGNEESTATENAME/>
                        <CMPGSTSTATE/>
                        <CONSIGNEECOUNTRYNAME/>
                        <BASICBASEPARTYNAME>Ekta Suppliers</BASICBASEPARTYNAME>
                        <NUMBERINGSTYLE>Auto Retain</NUMBERINGSTYLE>
                        <FBTPAYMENTTYPE>Default</FBTPAYMENTTYPE>
                        <PERSISTEDVIEW>Invoice Voucher View</PERSISTEDVIEW>
                        <VCHSTATUSTAXADJUSTMENT>Default</VCHSTATUSTAXADJUSTMENT>
                        <VCHSTATUSVOUCHERTYPE>AV-MachineryGRN</VCHSTATUSVOUCHERTYPE>
                        <VCHSTATUSTAXUNIT> Registration</VCHSTATUSTAXUNIT>
                        <COSTCENTRENAME>GENO</COSTCENTRENAME>
                        <DIFFACTUALQTY>No</DIFFACTUALQTY>
                        <ISOPTIONAL>No</ISOPTIONAL>
                        <ISINVOICE>No</ISINVOICE>
                        <HASDISCOUNTS>No</HASDISCOUNTS>
                        <ISCOSTCENTRE>Yes</ISCOSTCENTRE>
                        <VOUCHERNUMBERSERIES>Default</VOUCHERNUMBERSERIES>
                        <INVOICEORDERLIST.LIST>
                            <BASICORDERDATE/>
                            <ORDERTYPE>Purchase Order</ORDERTYPE>
                            <BASICPURCHASEORDERNO/>
                        </INVOICEORDERLIST.LIST>
                        <ALLINVENTORYENTRIES.LIST>
                            <BASICUSERDESCRIPTION.LIST TYPE="String">
                                <BASICUSERDESCRIPTION>Machine: MP 09 D.N 4447 (JCB)  |  Opening Time: 1882.5  |  Closing Time: 1884.1</BASICUSERDESCRIPTION>
                            </BASICUSERDESCRIPTION.LIST>
                            <STOCKITEMNAME>J.C.B. Hire Charges</STOCKITEMNAME>
                            <GSTOVRDNTAXABILITY>Taxable</GSTOVRDNTAXABILITY>
                            <GSTSOURCETYPE>Stock Item</GSTSOURCETYPE>
                            <GSTITEMSOURCE>J.C.B. Hire Charges</GSTITEMSOURCE>
                            <HSNSOURCETYPE>Stock Item</HSNSOURCETYPE>
                            <HSNITEMSOURCE>J.C.B. Hire Charges</HSNITEMSOURCE>
                            <GSTOVRDNTYPEOFSUPPLY>Goods</GSTOVRDNTYPEOFSUPPLY>
                            <GSTRATEINFERAPPLICABILITY>As per Masters/Company</GSTRATEINFERAPPLICABILITY>
                            <GSTHSNNAME/>
                            <GSTHSNINFERAPPLICABILITY>As per Masters/Company</GSTHSNINFERAPPLICABILITY>
                            <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                            <RATE>0.00</RATE>
                            <AMOUNT>-1.00</AMOUNT>
                            <ACTUALQTY>1.*************** Hrs</ACTUALQTY>
                            <BILLEDQTY>1.*************** Hrs</BILLEDQTY>
                            <BATCHALLOCATIONS.LIST>
                                <GODOWNNAME>Main Location</GODOWNNAME>
                                <BATCHNAME>Primary Batch</BATCHNAME>
                                <ORDERNO/>
                                <TRACKINGNUMBER>MP 09 D.N 4447 (JCB)</TRACKINGNUMBER>
                                <AMOUNT>-1.00</AMOUNT>
                                <ACTUALQTY>1.*************** Hrs</ACTUALQTY>
                                <BILLEDQTY>1.*************** Hrs</BILLEDQTY>
                                <ORDERDUEDATE>********</ORDERDUEDATE>
                            </BATCHALLOCATIONS.LIST>
                            <ACCOUNTINGALLOCATIONS.LIST>
                                <LEDGERNAME>Purchase Gst Services Received</LEDGERNAME>
                                <GSTCLASS>Not Applicable</GSTCLASS>
                                <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                                <AMOUNT>-1.00</AMOUNT>
                                <CATEGORYALLOCATIONS.LIST>
                                    <CATEGORY>Primary Cost Category</CATEGORY>
                                    <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                                    <COSTCENTREALLOCATIONS.LIST>
                                        <NAME>GENO</NAME>
                                        <AMOUNT>-1.00</AMOUNT>
                                    </COSTCENTREALLOCATIONS.LIST>
                                </CATEGORYALLOCATIONS.LIST>
                            </ACCOUNTINGALLOCATIONS.LIST>
                        </ALLINVENTORYENTRIES.LIST>
                        <LEDGERENTRIES.LIST>
                            <LEDGERNAME>Purchase Gst Services Received</LEDGERNAME>
                            <GSTCLASS>Not Applicable</GSTCLASS>
                            <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                            <AMOUNT>1.00</AMOUNT>
                            <VATEXPAMOUNT>1.00</VATEXPAMOUNT>
                            <CATEGORYALLOCATIONS.LIST>
                                <CATEGORY>Primary Cost Category</CATEGORY>
                                <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                                <COSTCENTREALLOCATIONS.LIST>
                                    <NAME>GENO</NAME>
                                    <AMOUNT>1.00</AMOUNT>
                                </COSTCENTREALLOCATIONS.LIST>
                            </CATEGORYALLOCATIONS.LIST>
                        </LEDGERENTRIES.LIST>
                    </VOUCHER>
                </TALLYMESSAGE>
            </REQUESTDATA>
        </IMPORTDATA>
    </BODY>
</ENVELOPE>
